<?php
/**
 * Plugin Name: Simple Contact Form Handler
 * Description: Simple REST API endpoint for contact form submissions
 * Version: 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add CORS headers
add_action('rest_api_init', function() {
    remove_filter('rest_pre_serve_request', 'rest_send_cors_headers');
    add_filter('rest_pre_serve_request', function($value) {
        header('Access-Control-Allow-Origin: https://bmoeventz.com');
        header('Access-Control-Allow-Origin: https://www.bmoeventz.com');
        header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type');
        return $value;
    });
});

// Register REST API endpoint
add_action('rest_api_init', function() {
    register_rest_route('contact/v1', '/submit', array(
        'methods' => 'POST',
        'callback' => 'handle_contact_submission',
        'permission_callback' => '__return_true'
    ));
});

function handle_contact_submission($request) {
    $params = $request->get_json_params();
    
    // Validate required fields
    if (empty($params['name']) || empty($params['email']) || empty($params['message'])) {
        return new WP_Error('missing_fields', 'Please fill in all required fields', array('status' => 400));
    }
    
    // Sanitize data
    $name = sanitize_text_field($params['name']);
    $email = sanitize_email($params['email']);
    $phone = sanitize_text_field($params['phone'] ?? '');
    $eventType = sanitize_text_field($params['eventType'] ?? '');
    $message = sanitize_textarea_field($params['message']);
    
    // Save to database
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';
    
    // Create table if it doesn't exist
    $wpdb->query("CREATE TABLE IF NOT EXISTS $table_name (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        email varchar(255) NOT NULL,
        phone varchar(50),
        event_type varchar(100),
        message text NOT NULL,
        submitted_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    )");
    
    // Insert submission
    $result = $wpdb->insert(
        $table_name,
        array(
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'event_type' => $eventType,
            'message' => $message
        )
    );
    
    if ($result === false) {
        return new WP_Error('db_error', 'Failed to save submission', array('status' => 500));
    }
    
    // Send email notification
    $admin_email = get_option('admin_email');
    $subject = 'New Contact Form Submission - BMO Eventz';
    $email_message = "New contact form submission:\n\n";
    $email_message .= "Name: $name\n";
    $email_message .= "Email: $email\n";
    $email_message .= "Phone: $phone\n";
    $email_message .= "Event Type: $eventType\n";
    $email_message .= "Message: $message\n";
    
    wp_mail($admin_email, $subject, $email_message);
    
    return array(
        'success' => true,
        'message' => 'Thank you! Your message has been sent successfully.'
    );
}

// Add admin menu to view submissions
add_action('admin_menu', function() {
    add_menu_page(
        'Contact Submissions',
        'Contact Forms',
        'manage_options',
        'contact-submissions',
        'display_contact_submissions',
        'dashicons-email-alt',
        30
    );
});

function display_contact_submissions() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';
    $submissions = $wpdb->get_results("SELECT * FROM $table_name ORDER BY submitted_at DESC");
    
    echo '<div class="wrap">';
    echo '<h1>Contact Form Submissions</h1>';
    echo '<table class="wp-list-table widefat fixed striped">';
    echo '<thead><tr><th>Name</th><th>Email</th><th>Phone</th><th>Event Type</th><th>Message</th><th>Date</th></tr></thead>';
    echo '<tbody>';
    
    foreach ($submissions as $submission) {
        echo '<tr>';
        echo '<td>' . esc_html($submission->name) . '</td>';
        echo '<td>' . esc_html($submission->email) . '</td>';
        echo '<td>' . esc_html($submission->phone) . '</td>';
        echo '<td>' . esc_html($submission->event_type) . '</td>';
        echo '<td>' . esc_html(substr($submission->message, 0, 100)) . '...</td>';
        echo '<td>' . esc_html($submission->submitted_at) . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody></table>';
    echo '</div>';
}
?>
