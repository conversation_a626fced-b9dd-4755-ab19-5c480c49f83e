import React, { useState, useEffect } from 'react';
import { X, Calendar, Package, Clock, Plus, Minus } from 'lucide-react';
import { useCart, RentalCartItem } from '../contexts/CartContext';
import { useGoogleAnalytics } from './GoogleAnalytics';

interface RentalItem {
  id: string;
  name: string;
  description: string;
  category: string;
  price: string;
  image: string;
  availability?: string;
  featured?: boolean;
  active: boolean;
  order?: number;
}

interface RentalModalProps {
  item: RentalItem | null;
  isOpen: boolean;
  onClose: () => void;
}

export function RentalModal({ item, isOpen, onClose }: RentalModalProps) {
  const { addItem } = useCart();
  const { trackAddToCart } = useGoogleAnalytics();
  const [quantity, setQuantity] = useState(1);
  const [rentalDays, setRentalDays] = useState(1);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [specialRequests, setSpecialRequests] = useState('');
  const [dailyRate, setDailyRate] = useState(0);

  // Extract daily rate from price string
  useEffect(() => {
    if (item?.price) {
      // Try to extract number from price string (e.g., "£25/day" -> 25)
      const match = item.price.match(/£?(\d+(?:\.\d{2})?)/);
      if (match) {
        setDailyRate(parseFloat(match[1]));
      } else {
        setDailyRate(25); // Default rate
      }
    }
  }, [item]);

  // Calculate end date when start date or rental days change
  useEffect(() => {
    if (startDate && rentalDays > 0) {
      const start = new Date(startDate);
      const end = new Date(start);
      end.setDate(start.getDate() + rentalDays - 1);
      setEndDate(end.toISOString().split('T')[0]);
    }
  }, [startDate, rentalDays]);

  // Calculate rental days when dates change
  useEffect(() => {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      if (diffDays > 0 && diffDays !== rentalDays) {
        setRentalDays(diffDays);
      }
    }
  }, [startDate, endDate]);

  const totalPrice = quantity * rentalDays * dailyRate;

  const handleAddToCart = () => {
    if (!item || !startDate || !endDate) return;

    const cartItem: RentalCartItem = {
      id: `${item.id}-${Date.now()}`, // Unique ID for cart item
      name: item.name,
      description: item.description,
      category: item.category,
      price: item.price,
      image: item.image,
      quantity,
      rentalDays,
      startDate,
      endDate,
      dailyRate,
      totalPrice,
      specialRequests: specialRequests || undefined,
    };

    addItem(cartItem);

    // Track add to cart event
    trackAddToCart(item.id, item.name, totalPrice);

    onClose();

    // Reset form
    setQuantity(1);
    setRentalDays(1);
    setStartDate('');
    setEndDate('');
    setSpecialRequests('');
  };

  const getTomorrowDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  if (!isOpen || !item) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-[#30062f]">Configure Rental</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Item Info */}
          <div className="flex gap-4 mb-6">
            <div className="w-24 h-24 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
              {item.image ? (
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Package size={32} className="text-gray-400" />
                </div>
              )}
            </div>
            <div>
              <h3 className="text-xl font-bold text-[#30062f] mb-2">{item.name}</h3>
              <p className="text-gray-600 text-sm mb-2">{item.description}</p>
              <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {item.category}
              </span>
            </div>
          </div>

          {/* Rental Configuration */}
          <div className="space-y-6">
            {/* Quantity */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantity
              </label>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  <Minus size={16} />
                </button>
                <span className="text-lg font-medium w-12 text-center">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  <Plus size={16} />
                </button>
              </div>
            </div>

            {/* Rental Dates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar size={16} className="inline mr-1" />
                  Start Date
                </label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  min={getTomorrowDate()}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Calendar size={16} className="inline mr-1" />
                  End Date
                </label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  min={startDate || getTomorrowDate()}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                  required
                />
              </div>
            </div>

            {/* Rental Duration */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock size={16} className="inline mr-1" />
                Rental Duration
              </label>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setRentalDays(Math.max(1, rentalDays - 1))}
                  className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  <Minus size={16} />
                </button>
                <span className="text-lg font-medium w-16 text-center">
                  {rentalDays} day{rentalDays !== 1 ? 's' : ''}
                </span>
                <button
                  onClick={() => setRentalDays(rentalDays + 1)}
                  className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  <Plus size={16} />
                </button>
              </div>
            </div>

            {/* Special Requests */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Special Requests (Optional)
              </label>
              <textarea
                value={specialRequests}
                onChange={(e) => setSpecialRequests(e.target.value)}
                placeholder="Any special requirements or notes..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f] h-20 resize-none"
              />
            </div>

            {/* Price Breakdown */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-700 mb-3">Price Breakdown</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Daily Rate:</span>
                  <span>£{dailyRate.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Quantity:</span>
                  <span>{quantity} item{quantity !== 1 ? 's' : ''}</span>
                </div>
                <div className="flex justify-between">
                  <span>Duration:</span>
                  <span>{rentalDays} day{rentalDays !== 1 ? 's' : ''}</span>
                </div>
                <div className="border-t pt-2 flex justify-between font-bold text-lg">
                  <span>Total:</span>
                  <span className="text-[#30062f]">£{totalPrice.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-6 border-t">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleAddToCart}
            disabled={!startDate || !endDate}
            className="flex-1 px-4 py-2 bg-[#30062f] text-white rounded-md hover:bg-[#4a0a4b] transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            Add to Cart - £{totalPrice.toFixed(2)}
          </button>
        </div>
      </div>
    </div>
  );
}
