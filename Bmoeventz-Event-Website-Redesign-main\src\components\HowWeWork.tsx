import React from 'react';
export function HowWeWork() {
  const steps = [{
    number: '01',
    title: 'Vision Call',
    description: 'We start with understanding your goals, expectations, and event vision.'
  }, {
    number: '02',
    title: 'Proposal & Timeline',
    description: 'We create a detailed plan with timeline, budget, and execution strategy.'
  }, {
    number: '03',
    title: 'Coordination',
    description: 'We manage vendors, logistics, and all details leading up to your event.'
  }, {
    number: '04',
    title: 'Delivery & Debrief',
    description: 'We execute flawlessly on the day and follow up with a post-event debrief.'
  }];
  return <section className="py-16 md:py-24 bg-[#30062f]">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            How We Work
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            Our proven process ensures your event is meticulously planned and
            flawlessly executed.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {steps.map((step, index) => <div key={index} className="bg-white/10 backdrop-blur-sm p-8 rounded-lg border border-white/20 hover:bg-white/20 transition-colors">
              <div className="text-[#a0224b] text-4xl font-bold mb-4">
                {step.number}
              </div>
              <h3 className="text-xl font-bold text-white mb-3">
                {step.title}
              </h3>
              <p className="text-gray-300">{step.description}</p>
            </div>)}
        </div>
      </div>
    </section>;
}