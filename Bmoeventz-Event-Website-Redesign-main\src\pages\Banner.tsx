import React, { useState } from 'react';
import { Download, Eye, X } from 'lucide-react';

export function Banner() {
  const [showBrochure, setShowBrochure] = useState(false);
  const [showBanner, setShowBanner] = useState(false);
  const [currentBrochurePage, setCurrentBrochurePage] = useState(0);

  const brochureImages = [
    '/images/rental-brochure-1.png',
    '/images/rental-brochure-2.png'
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#30062f] via-[#4a0a4b] to-[#6b1b7f] py-12 px-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Marketing Materials
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Our company marketing materials and rental brochures
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Company Banner Section */}
          <div className="bg-white bg-opacity-10 p-8 rounded-lg backdrop-blur-sm">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              Company Banner
            </h2>

            {/* Banner Image */}
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-6 rounded-lg mb-6 flex justify-center items-center min-h-[500px]">
              <img
                src="/images/roll-up-banner.png"
                alt="BMO Eventz Roll-up Banner"
                className="max-h-[480px] w-auto rounded-lg shadow-2xl border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
                style={{ objectFit: 'contain' }}
                onClick={() => setShowBanner(true)}
                onLoad={() => console.log('Banner image loaded successfully')}
                onError={(e) => {
                  console.log('Banner image failed to load:', e);
                  const target = e.target as HTMLImageElement;
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjUwMCIgdmlld0JveD0iMCAwIDMwMCA1MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNTAwIiBmaWxsPSIjRjNGNEY2IiBzdHJva2U9IiNEMUQ1REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWRhc2hhcnJheT0iMTAgMTAiLz4KPHA+PHRleHQgeD0iMTUwIiB5PSIyMzAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM2QjcyODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZm9udC13ZWlnaHQ9ImJvbGQiPkJNTyBFdmVudHo8L3RleHQ+Cjx0ZXh0IHg9IjE1MCIgeT0iMjUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlJvbGwtdXAgQmFubmVyPC90ZXh0Pgo8dGV4dCB4PSIxNTAiIHk9IjI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzlCOUJBMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIj5QbGVhc2UgdXBsb2FkIGJhbm5lciBpbWFnZTwvdGV4dD4KPC9zdmc+';
                }}
              />
            </div>

            {/* View Full Banner Button */}
            <div className="text-center mb-6">
              <button
                onClick={() => setShowBanner(true)}
                className="bg-[#a0224b] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#8a1e40] transition-colors flex items-center justify-center mx-auto"
              >
                <Eye className="mr-2" size={20} />
                View Full Banner
              </button>
            </div>


          </div>

          {/* Rental Brochure Section */}
          <div className="bg-white bg-opacity-10 p-8 rounded-lg backdrop-blur-sm">
            <h2 className="text-2xl font-bold text-white mb-6 text-center">
              Rental Brochure
            </h2>

            {/* Brochure Preview */}
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-lg mb-6">
              <img
                src={brochureImages[0]}
                alt="BMO Eventz Rental Brochure Preview"
                className="w-full h-64 object-cover rounded-lg shadow-lg cursor-pointer hover:opacity-90 transition-opacity border border-gray-200"
                onClick={() => setShowBrochure(true)}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI1NiIgdmlld0JveD0iMCAwIDQwMCAyNTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjU2IiBmaWxsPSIjRjNGNEY2IiBzdHJva2U9IiNEMUQ1REIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWRhc2hhcnJheT0iMTAgMTAiLz4KPHA+PHRleHQgeD0iMjAwIiB5PSIxMTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM2QjcyODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZm9udC13ZWlnaHQ9ImJvbGQiPkJNTyBFdmVudHo8L3RleHQ+Cjx0ZXh0IHg9IjIwMCIgeT0iMTMwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlJlbnRhbCBCcm9jaHVyZTwvdGV4dD4KPHRleHQgeD0iMjAwIiB5PSIxNjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5QjlCQTAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiI+UGxlYXNlIHVwbG9hZCBicm9jaHVyZSBpbWFnZXM8L3RleHQ+Cjwvc3ZnPgo=';
                }}
              />
            </div>

            {/* Brochure Actions */}
            <div className="space-y-4">
              <button
                onClick={() => setShowBrochure(true)}
                className="w-full bg-[#a0224b] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#8a1e40] transition-colors flex items-center justify-center"
              >
                <Eye className="mr-2" size={20} />
                View Full Brochure
              </button>


            </div>
          </div>
        </div>


      </div>

      {/* Banner Modal */}
      {showBanner && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl w-full max-h-full">
            {/* Close Button */}
            <button
              onClick={() => setShowBanner(false)}
              className="absolute top-4 right-4 z-10 bg-black bg-opacity-70 text-white p-3 rounded-full hover:bg-opacity-90 transition-colors shadow-lg border-2 border-white"
            >
              <X size={24} />
            </button>

            {/* Banner Image */}
            <div className="bg-white p-6 rounded-lg shadow-2xl">
              <img
                src="/images/roll-up-banner.png"
                alt="BMO Eventz Roll-up Banner - Full View"
                className="w-full h-auto max-h-[85vh] object-contain rounded-lg"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjgwMCIgdmlld0JveD0iMCAwIDYwMCA4MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2MDAiIGhlaWdodD0iODAwIiBmaWxsPSIjRjNGNEY2IiBzdHJva2U9IiNEMUQ1REIiIHN0cm9rZS13aWR0aD0iMiIvPgo8dGV4dCB4PSIzMDAiIHk9IjM4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzZCNzI4MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCI+Qk1PIEV2ZW50ejwvdGV4dD4KPHA+PHRleHQgeD0iMzAwIiB5PSI0MjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM2QjcyODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCI+Um9sbC11cCBCYW5uZXI8L3RleHQ+Cjx0ZXh0IHg9IjMwMCIgeT0iNDUwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPlBsZWFzZSB1cGxvYWQgYmFubmVyIGltYWdlPC90ZXh0Pgo8L3N2Zz4K';
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Brochure Modal */}
      {showBrochure && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl w-full max-h-full">
            {/* Close Button */}
            <button
              onClick={() => setShowBrochure(false)}
              className="absolute top-4 right-4 z-10 bg-black bg-opacity-70 text-white p-3 rounded-full hover:bg-opacity-90 transition-colors shadow-lg border-2 border-white"
            >
              <X size={24} />
            </button>

            {/* Brochure Image */}
            <div className="bg-white p-6 rounded-lg shadow-2xl">
              <img
                src={brochureImages[currentBrochurePage]}
                alt={`BMO Eventz Rental Brochure Page ${currentBrochurePage + 1}`}
                className="w-full h-auto max-h-[80vh] object-contain rounded-lg"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDYwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjRjNGNEY2IiBzdHJva2U9IiNEMUQ1REIiIHN0cm9rZS13aWR0aD0iMiIvPgo8dGV4dCB4PSIzMDAiIHk9IjE4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzZCNzI4MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCI+Qk1PIEV2ZW50ejwvdGV4dD4KPHA+PHRleHQgeD0iMzAwIiB5PSIyMTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM2QjcyODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCI+UmVudGFsIEJyb2NodXJlPC90ZXh0Pgo8dGV4dCB4PSIzMDAiIHk9IjI0MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzlCOUJBMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0Ij5QbGVhc2UgdXBsb2FkIGJyb2NodXJlIGltYWdlczwvdGV4dD4KPC9zdmc+';
                }}
              />
            </div>

            {/* Navigation */}
            <div className="flex justify-center mt-4 space-x-4">
              {brochureImages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentBrochurePage(index)}
                  className={`px-4 py-2 rounded ${
                    currentBrochurePage === index
                      ? 'bg-[#a0224b] text-white'
                      : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'
                  } transition-colors`}
                >
                  Page {index + 1}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
