# WordPress Headless Setup Guide for BMO Eventz

## Prerequisites
- WordPress installation with admin access
- WPGraphQL plugin (already installed but not activated)

## Step 1: Activate Required Plugins

1. **Activate WPGraphQL Plugin**
   - Go to WordPress Admin → Plugins
   - Find "WPGraphQL" and click "Activate"

2. **Install Additional Recommended Plugins**
   ```
   - WPGraphQL for Advanced Custom Fields (if using ACF)
   - WPGraphQL CORS (for cross-origin requests)
   ```

## Step 2: Install Custom Contact Form Plugin

1. Upload the `wordpress-plugin/bmo-eventz-contact-form.php` file to your WordPress plugins directory:
   ```
   /wp-content/plugins/bmo-eventz-contact-form/bmo-eventz-contact-form.php
   ```

2. Activate the plugin in WordPress Admin → Plugins

## Step 3: Configure CORS Headers

Add this to your WordPress `wp-config.php` file or use a CORS plugin:

```php
// Enable CORS for GraphQL
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}
```

## Step 4: Create Custom Post Types

Add this to your theme's `functions.php` file:

```php
// Register Events Custom Post Type
function register_events_post_type() {
    register_post_type('events', [
        'labels' => [
            'name' => 'Events',
            'singular_name' => 'Event',
        ],
        'public' => true,
        'show_in_graphql' => true,
        'graphql_single_name' => 'event',
        'graphql_plural_name' => 'events',
        'supports' => ['title', 'editor', 'thumbnail'],
        'menu_icon' => 'dashicons-calendar-alt',
    ]);
}
add_action('init', 'register_events_post_type');

// Register Services Custom Post Type
function register_services_post_type() {
    register_post_type('services', [
        'labels' => [
            'name' => 'Services',
            'singular_name' => 'Service',
        ],
        'public' => true,
        'show_in_graphql' => true,
        'graphql_single_name' => 'service',
        'graphql_plural_name' => 'services',
        'supports' => ['title', 'editor', 'thumbnail'],
        'menu_icon' => 'dashicons-admin-tools',
    ]);
}
add_action('init', 'register_services_post_type');

// Register Gallery Custom Post Type
function register_gallery_post_type() {
    register_post_type('gallery_items', [
        'labels' => [
            'name' => 'Gallery',
            'singular_name' => 'Gallery Item',
        ],
        'public' => true,
        'show_in_graphql' => true,
        'graphql_single_name' => 'galleryItem',
        'graphql_plural_name' => 'galleryItems',
        'supports' => ['title', 'thumbnail'],
        'menu_icon' => 'dashicons-format-gallery',
    ]);
}
add_action('init', 'register_gallery_post_type');
```

## Step 5: Update Frontend Environment

1. Copy `.env.example` to `.env` in your React project
2. Update the WordPress GraphQL URL:
   ```
   REACT_APP_WORDPRESS_GRAPHQL_URL=https://your-wordpress-site.com/graphql
   ```

## Step 6: Test GraphQL Endpoint

1. Visit: `https://your-wordpress-site.com/graphql`
2. You should see the GraphQL IDE
3. Test with a simple query:
   ```graphql
   query {
     posts {
       nodes {
         title
         content
       }
     }
   }
   ```

## Step 7: Content Management

### Adding Services
1. Go to WordPress Admin → Services → Add New
2. Add title, description, and featured image
3. Content will automatically appear in your React frontend

### Adding Events
1. Go to WordPress Admin → Events → Add New
2. Add event details, date, and featured image

### Adding Gallery Items
1. Go to WordPress Admin → Gallery → Add New
2. Upload images and add descriptions

### Managing Contact Form Submissions
1. Go to WordPress Admin → Contact Forms
2. View all submissions with details
3. Email notifications are sent automatically

## Step 8: Advanced Custom Fields (Optional)

If you want more structured data, install ACF and add field groups:

### Service Fields
- Price (Number)
- Features (Repeater)
- Description (Textarea)

### Event Fields
- Event Date (Date Picker)
- Location (Text)
- Capacity (Number)
- Price (Number)

### Gallery Fields
- Category (Select)
- Description (Textarea)

## Security Considerations

1. **API Rate Limiting**: Consider implementing rate limiting
2. **Authentication**: Add authentication for sensitive operations
3. **Validation**: All inputs are sanitized in the contact form plugin
4. **CORS**: Restrict CORS to your specific domain in production

## Production Deployment

1. Update CORS headers to use your production domain
2. Update `.env` file with production WordPress URL
3. Ensure SSL certificates are properly configured
4. Test all GraphQL queries in production environment

## Troubleshooting

### Common Issues:
1. **CORS Errors**: Check CORS configuration
2. **GraphQL Not Working**: Ensure WPGraphQL plugin is activated
3. **Contact Form Not Submitting**: Check plugin activation and database permissions
4. **Custom Post Types Not Showing**: Verify `show_in_graphql` is set to true

### Debug Mode:
Add to `wp-config.php` for debugging:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('GRAPHQL_DEBUG', true);
```
