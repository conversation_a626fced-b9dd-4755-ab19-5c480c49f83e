<?php
/**
 * Plugin Name: BMO Eventz Contact Form Handler
 * Description: <PERSON>les contact form submissions from the React frontend via GraphQL
 * Version: 1.0.0
 * Author: BMO Eventz
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class BMOEventzContactForm {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('graphql_register_types', array($this, 'register_graphql_types'));
    }
    
    public function init() {
        // Create contact submissions table
        $this->create_contact_table();
    }
    
    public function create_contact_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'bmo_contact_submissions';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name tinytext NOT NULL,
            email varchar(100) NOT NULL,
            phone varchar(20),
            message text NOT NULL,
            submission_date datetime DEFAULT CURRENT_TIMESTAMP,
            status varchar(20) DEFAULT 'new',
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    public function register_graphql_types() {
        // Register input type for contact form
        register_graphql_input_type('ContactFormInput', [
            'description' => 'Input for contact form submission',
            'fields' => [
                'name' => [
                    'type' => 'String',
                    'description' => 'Contact name',
                ],
                'email' => [
                    'type' => 'String',
                    'description' => 'Contact email',
                ],
                'phone' => [
                    'type' => 'String',
                    'description' => 'Contact phone number',
                ],
                'message' => [
                    'type' => 'String',
                    'description' => 'Contact message',
                ],
            ],
        ]);
        
        // Register output type for contact form response
        register_graphql_object_type('ContactFormResponse', [
            'description' => 'Response from contact form submission',
            'fields' => [
                'success' => [
                    'type' => 'Boolean',
                    'description' => 'Whether the submission was successful',
                ],
                'message' => [
                    'type' => 'String',
                    'description' => 'Response message',
                ],
            ],
        ]);
        
        // Register mutation for contact form submission
        register_graphql_mutation('submitContactForm', [
            'inputFields' => [
                'input' => [
                    'type' => 'ContactFormInput',
                    'description' => 'Contact form data',
                ],
            ],
            'outputFields' => [
                'response' => [
                    'type' => 'ContactFormResponse',
                    'description' => 'Submission response',
                ],
            ],
            'mutateAndGetPayload' => function($input) {
                return $this->handle_contact_submission($input['input']);
            },
        ]);
    }
    
    public function handle_contact_submission($input) {
        global $wpdb;
        
        // Validate input
        if (empty($input['name']) || empty($input['email']) || empty($input['message'])) {
            return [
                'response' => [
                    'success' => false,
                    'message' => 'Please fill in all required fields.',
                ]
            ];
        }
        
        // Sanitize input
        $name = sanitize_text_field($input['name']);
        $email = sanitize_email($input['email']);
        $phone = sanitize_text_field($input['phone'] ?? '');
        $message = sanitize_textarea_field($input['message']);
        
        // Validate email
        if (!is_email($email)) {
            return [
                'response' => [
                    'success' => false,
                    'message' => 'Please enter a valid email address.',
                ]
            ];
        }
        
        // Save to database
        $table_name = $wpdb->prefix . 'bmo_contact_submissions';
        $result = $wpdb->insert(
            $table_name,
            [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'message' => $message,
            ]
        );
        
        if ($result === false) {
            return [
                'response' => [
                    'success' => false,
                    'message' => 'There was an error saving your message. Please try again.',
                ]
            ];
        }
        
        // Send email notification
        $this->send_email_notification($name, $email, $phone, $message);
        
        return [
            'response' => [
                'success' => true,
                'message' => 'Thank you for your message! We will get back to you within 24 hours.',
            ]
        ];
    }
    
    private function send_email_notification($name, $email, $phone, $message) {
        $admin_email = get_option('admin_email');
        $site_name = get_bloginfo('name');
        
        $subject = "New Contact Form Submission - $site_name";
        
        $email_message = "
        New contact form submission received:
        
        Name: $name
        Email: $email
        Phone: $phone
        
        Message:
        $message
        
        ---
        This message was sent from the BMO Eventz website contact form.
        ";
        
        $headers = [
            'Content-Type: text/plain; charset=UTF-8',
            "From: $site_name <$admin_email>",
            "Reply-To: $name <$email>"
        ];
        
        wp_mail($admin_email, $subject, $email_message, $headers);
    }
}

// Initialize the plugin
new BMOEventzContactForm();

// Add admin menu for viewing submissions
add_action('admin_menu', function() {
    add_menu_page(
        'Contact Submissions',
        'Contact Forms',
        'manage_options',
        'bmo-contact-submissions',
        'bmo_contact_submissions_page',
        'dashicons-email-alt',
        30
    );
});

function bmo_contact_submissions_page() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'bmo_contact_submissions';
    $submissions = $wpdb->get_results("SELECT * FROM $table_name ORDER BY submission_date DESC");
    
    echo '<div class="wrap">';
    echo '<h1>Contact Form Submissions</h1>';
    echo '<table class="wp-list-table widefat fixed striped">';
    echo '<thead><tr><th>Date</th><th>Name</th><th>Email</th><th>Phone</th><th>Message</th><th>Status</th></tr></thead>';
    echo '<tbody>';
    
    foreach ($submissions as $submission) {
        echo '<tr>';
        echo '<td>' . esc_html($submission->submission_date) . '</td>';
        echo '<td>' . esc_html($submission->name) . '</td>';
        echo '<td>' . esc_html($submission->email) . '</td>';
        echo '<td>' . esc_html($submission->phone) . '</td>';
        echo '<td>' . esc_html(substr($submission->message, 0, 100)) . '...</td>';
        echo '<td>' . esc_html($submission->status) . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody></table>';
    echo '</div>';
}
?>
