import React from 'react';
import { Helmet } from 'react-helmet-async';

interface GoogleAnalyticsProps {
  measurementId?: string;
}

export function GoogleAnalytics({ measurementId }: GoogleAnalyticsProps) {
  // Use environment variable or provided measurement ID
  const GA_MEASUREMENT_ID = measurementId || import.meta.env.VITE_GA_MEASUREMENT_ID;

  // Don't render in development unless explicitly enabled
  const isDevelopment = import.meta.env.DEV;
  const enableInDev = import.meta.env.VITE_GA_ENABLE_IN_DEV === 'true';

  if (isDevelopment && !enableInDev) {
    return null;
  }

  if (!GA_MEASUREMENT_ID) {
    console.warn('Google Analytics Measurement ID not found. Add VITE_GA_MEASUREMENT_ID to your .env file.');
    return null;
  }

  return (
    <Helmet>
      {/* Google Analytics 4 */}
      <script
        async
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <script>
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${GA_MEASUREMENT_ID}', {
            page_title: document.title,
            page_location: window.location.href,
            send_page_view: true
          });
        `}
      </script>
    </Helmet>
  );
}

// Hook for tracking custom events
export function useGoogleAnalytics() {
  const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, parameters);
    }
  };

  const trackPageView = (pagePath: string, pageTitle?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', import.meta.env.VITE_GA_MEASUREMENT_ID, {
        page_path: pagePath,
        page_title: pageTitle || document.title,
      });
    }
  };

  const trackPurchase = (transactionId: string, value: number, currency: string = 'GBP', items: any[] = []) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'purchase', {
        transaction_id: transactionId,
        value: value,
        currency: currency,
        items: items
      });
    }
  };

  const trackAddToCart = (itemId: string, itemName: string, value: number, currency: string = 'GBP') => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'add_to_cart', {
        currency: currency,
        value: value,
        items: [{
          item_id: itemId,
          item_name: itemName,
          currency: currency,
          value: value
        }]
      });
    }
  };

  const trackFormSubmit = (formName: string, formId?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'form_submit', {
        form_name: formName,
        form_id: formId
      });
    }
  };

  const trackContactAttempt = (method: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'contact_attempt', {
        contact_method: method
      });
    }
  };

  return {
    trackEvent,
    trackPageView,
    trackPurchase,
    trackAddToCart,
    trackFormSubmit,
    trackContactAttempt
  };
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}
