import React from 'react';
import { Link } from 'react-router-dom';
export function Gallery() {
  const galleryItems = [{
    image: 'https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Corporate Setups'
  }, {
    image: 'https://images.unsplash.com/photo-1519225421980-715cb0215aed?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Styled Dinners'
  }, {
    image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Community Eventz'
  }];
  return <section className="py-16 md:py-24 bg-[#F9F5FF]">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-4">
            Gallery Highlights
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            A glimpse of our recent events showcasing our planning, styling, and
            execution expertise.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {galleryItems.map((item, index) => <div key={index} className="relative group overflow-hidden rounded-lg shadow-lg h-64 bg-[#30062f]">
              <img src={item.image} alt={item.category} className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" />
              <div className="absolute inset-0 bg-gradient-to-t from-[#30062f] to-transparent opacity-70"></div>
              <div className="absolute bottom-0 left-0 right-0 p-4">
                <h3 className="text-white font-medium text-lg">
                  {item.category}
                </h3>
              </div>
            </div>)}
        </div>
        <div className="text-center mt-10">
          <Link to="/gallery" className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors">
            View All Projects
          </Link>
        </div>
      </div>
    </section>;
}