import React, { memo } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Users, Award, Heart, Clock } from 'lucide-react';
import { AboutSEO } from '../components/SEO';
export function About() {
  return <>
      <AboutSEO />
      {/* About Hero */}
      <section className="relative w-full bg-cover bg-center py-20 md:py-28 bg-[#30062f]" style={{
      backgroundImage: `linear-gradient(rgba(48, 6, 47, 0.4), rgba(48, 6, 47, 0.4)), url('https://images.unsplash.com/photo-1540317580384-e5d43867caa6?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`,
      backgroundPosition: 'center',
      backgroundSize: 'cover'
    }}>
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            About BMO Eventz
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            We're passionate about curating atmosphere, order and impact for
            memorable events
          </p>
        </div>
      </section>
      {/* Our Story */}
      <section className="py-16 md:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-6">
                Our Story
              </h2>
              <p className="text-gray-600 mb-6">
                At BMO Eventz, we are passionate about curating atmosphere,
                order and impact. Our journey began with a simple vision: to
                help people create meaningful events without the stress and
                overwhelm that often comes with event planning.
              </p>
              <p className="text-gray-600 mb-6">
                Founded by a team of experienced event professionals, we
                understand the importance of precision, creativity, and
                attention to detail. We've spent years refining our approach to
                event planning, styling, and coordination to ensure every client
                receives an exceptional experience.
              </p>
              <p className="text-gray-600">
                Whether you're planning a corporate dinner, church conference,
                wedding, kids birthday party, or launch event, we bring clarity, coordination, and
                creative styling that reflects your goals and exceeds your
                expectations.
              </p>
            </div>
            <div className="rounded-lg overflow-hidden shadow-xl bg-[#30062f]">
              <img src="https://images.unsplash.com/photo-1475721027785-f74eccf877e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80" alt="BMO Eventz Team" className="w-full h-full object-cover" />
            </div>
          </div>
        </div>
      </section>
      {/* Our Values */}
      <section className="py-16 md:py-24 bg-[#F9F5FF]">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-4">
              Our Values
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              The principles that guide everything we do at BMO Eventz
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Excellence */}
            <div className="bg-white p-8 rounded-lg shadow-lg flex flex-col items-center text-center hover:shadow-xl transition-shadow">
              <div className="bg-[#30062f] p-4 rounded-full mb-6">
                <Award size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#30062f] mb-3">
                Excellence
              </h3>
              <p className="text-gray-600">
                We're committed to delivering the highest standard in everything
                we do, from planning to execution.
              </p>
            </div>
            {/* Collaboration */}
            <div className="bg-white p-8 rounded-lg shadow-lg flex flex-col items-center text-center hover:shadow-xl transition-shadow">
              <div className="bg-[#30062f] p-4 rounded-full mb-6">
                <Users size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#30062f] mb-3">
                Collaboration
              </h3>
              <p className="text-gray-600">
                We work closely with our clients and vendors to ensure every
                vision becomes reality.
              </p>
            </div>
            {/* Passion */}
            <div className="bg-white p-8 rounded-lg shadow-lg flex flex-col items-center text-center hover:shadow-xl transition-shadow">
              <div className="bg-[#30062f] p-4 rounded-full mb-6">
                <Heart size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#30062f] mb-3">Passion</h3>
              <p className="text-gray-600">
                We genuinely love what we do, and that enthusiasm shines through
                in every event we plan.
              </p>
            </div>
            {/* Reliability */}
            <div className="bg-white p-8 rounded-lg shadow-lg flex flex-col items-center text-center hover:shadow-xl transition-shadow">
              <div className="bg-[#30062f] p-4 rounded-full mb-6">
                <Clock size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#30062f] mb-3">
                Reliability
              </h3>
              <p className="text-gray-600">
                We deliver on our promises, ensuring peace of mind throughout
                the entire planning process.
              </p>
            </div>
          </div>
        </div>
      </section>
      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-[#30062f] to-[#4a0a4b]">
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to plan your next event with us?
          </h2>
          <p className="text-lg text-white/80 mb-8 max-w-3xl mx-auto">
            Let's create a memorable experience together that your guests will
            be talking about for years to come.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Link to="/contact" className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors" onClick={() => window.scrollTo({top: 0, behavior: 'smooth'})}>
              Get in Touch
            </Link>
            <Link to="/services" className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-md font-medium hover:bg-white hover:text-[#30062f] transition-colors">
              Explore Our Services
            </Link>
          </div>
        </div>
      </section>
    </>;
}

