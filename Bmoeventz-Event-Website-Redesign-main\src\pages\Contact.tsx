import React, { useState } from 'react';
import { Phone, Mail, MapPin, Clock, Calendar, CreditCard } from 'lucide-react';
import { useSiteSettings } from '../hooks/useContentful';
import { ContactSEO } from '../components/SEO';
import { useGoogleAnalytics } from '../components/GoogleAnalytics';
import { ConsultationBooking } from '../components/ConsultationBooking';

// Professional Consultation Component
function ConsultationSection({ onBookConsultation }: { onBookConsultation: () => void }) {
  return (
    <div className="bg-[#F9F5FF] p-6 rounded-lg">
      <h3 className="text-lg font-bold text-[#30062f] mb-3">
        Need Professional Consultation?
      </h3>
      <p className="text-gray-600 mb-4">
        Get personalized expert advice from our experienced event planners. We offer focused consultation sessions to help bring your vision to life with professional guidance.
      </p>

      <div className="space-y-3 mb-6">
        <div className="flex items-center text-sm text-gray-600">
          <Calendar size={16} className="mr-2 text-[#30062f]" />
          <span>Flexible scheduling options</span>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <Clock size={16} className="mr-2 text-[#30062f]" />
          <span>Expert guidance and planning</span>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <MapPin size={16} className="mr-2 text-[#30062f]" />
          <span>Tailored to your event needs</span>
        </div>
      </div>

      <button
        onClick={onBookConsultation}
        className="w-full bg-[#a0224b] text-white px-4 py-2 rounded-md hover:bg-[#8a1e40] transition-colors font-medium"
      >
        Book Professional Consultation
      </button>
    </div>
  );
}

export function Contact() {
  const { trackFormSubmit, trackContactAttempt } = useGoogleAnalytics();
  const [isConsultationModalOpen, setIsConsultationModalOpen] = useState(false);

  // Site Settings integration with error handling
  let settings = null;
  let settingsLoading = false;
  let settingsError = null;

  try {
    const siteSettingsResult = useSiteSettings();
    settings = siteSettingsResult.settings;
    settingsLoading = siteSettingsResult.loading;
    settingsError = siteSettingsResult.error;
  } catch (err) {
    console.warn('Site Settings hook failed:', err);
    // Use fallback values
    settings = null;
    settingsLoading = false;
    settingsError = 'Failed to load site settings';
  }
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    eventType: '',
    message: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Create form data for Formsubmit.co (completely free)
      const formDataToSend = new FormData();
      formDataToSend.append('name', formData.name);
      formDataToSend.append('email', formData.email);
      formDataToSend.append('phone', formData.phone);
      formDataToSend.append('eventType', formData.eventType);
      formDataToSend.append('message', `Event Type: ${formData.eventType}\n\n${formData.message}`);
      formDataToSend.append('_subject', 'New Contact Form Submission - BMO Eventz');
      formDataToSend.append('_captcha', 'false'); // Disable captcha
      formDataToSend.append('_template', 'table'); // Nice email format

      // Using your Formsubmit.co code
      const response = await fetch('https://formsubmit.co/eac6395a8f2862975841aa939ba97f6f', {
        method: 'POST',
        body: formDataToSend
      });

      if (response.ok) {
        setIsSubmitted(true);

        // Track successful form submission
        trackFormSubmit('contact_form', 'main-contact-form');
        trackContactAttempt('contact_form');

        setFormData({
          name: '',
          email: '',
          phone: '',
          eventType: '',
          message: ''
        });
      } else {
        setError('Form submission failed. Please try again.');
      }
    } catch (error) {
      setError('There was an error sending your message. Please try again.');
      console.error('Form submission error:', error);
    } finally {
      setLoading(false);
    }
  };

  return <>
      <ContactSEO />
      {/* Contact Hero */}
      <section className="relative w-full bg-cover bg-center py-20 md:py-28 bg-[#30062f]" style={{
        backgroundImage: `linear-gradient(rgba(48, 6, 47, 0.4), rgba(48, 6, 47, 0.4)), url('https://images.unsplash.com/photo-1528605248644-14dd04022da1?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`,
        backgroundPosition: 'center',
        backgroundSize: 'cover'
      }}>
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Get in Touch
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            Let's talk through your event vision today and create something
            memorable together
          </p>
        </div>
      </section>
      {/* Contact Content */}
      <section className="py-16 md:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold text-[#30062f] mb-6">
                Send Us a Message
              </h2>
              <p className="text-gray-600 mb-8">
                Fill out the form below and we'll get back to you within 24
                hours to discuss your event needs.
              </p>
              {isSubmitted && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                  Thank you! Your message has been sent successfully. We'll get back to you within 24 hours.
                </div>
              )}

              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                  There was an error sending your message. Please try again or contact us directly.
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                      placeholder="Your Name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                    placeholder="Your Phone Number"
                  />
                </div>
                <div>
                  <label htmlFor="event-type" className="block text-sm font-medium text-gray-700 mb-1">
                    Event Type *
                  </label>
                  <select
                    id="event-type"
                    name="eventType"
                    value={formData.eventType}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                  >
                    <option value="" disabled>
                      Select Event Type
                    </option>
                    <option value="corporate">Corporate Event</option>
                    <option value="wedding">Wedding</option>
                    <option value="social">Social Gathering</option>
                    <option value="conference">Conference</option>
                    <option value="csr">CSR Event</option>
                    <option value="kids-party">Kids Party</option>
                    <option value="other">Other</option>
                  </select>
                </div>



                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                    Your Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={5}
                    className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                    placeholder="Tell us about your event and how we can help..."
                  ></textarea>
                </div>
                <div>
                  <button
                    type="submit"
                    disabled={loading}
                    className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'Sending...' : 'Send Message'}
                  </button>
                </div>
              </form>
            </div>
            {/* Contact Information */}
            <div>
              <h2 className="text-3xl font-bold text-[#30062f] mb-6">
                Contact Information
              </h2>
              <p className="text-gray-600 mb-8">
                Have questions or need immediate assistance? Reach out to us
                directly using the information below.
              </p>
              <div className="space-y-6 mb-12">
                {settingsLoading && (
                  <div className="text-center py-4">
                    <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-[#30062f]"></div>
                    <p className="mt-2 text-gray-600 text-sm">Loading contact info...</p>
                  </div>
                )}

                {settingsError && (
                  <div className="text-center py-4">
                    <p className="text-red-600 text-sm">Error loading contact info</p>
                  </div>
                )}

                <div className="flex items-start">
                  <div className="bg-[#30062f] p-3 rounded-full mr-4">
                    <Phone size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-[#30062f]">Phone</h3>
                    <p className="text-gray-600">{settings?.phoneNumber || '+44 01908739814'}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-[#30062f] p-3 rounded-full mr-4">
                    <Mail size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-[#30062f]">Email</h3>
                    <p className="text-gray-600">{settings?.emailAddress || '<EMAIL>'}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-[#30062f] p-3 rounded-full mr-4">
                    <MapPin size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-[#30062f]">
                      Office Address
                    </h3>
                    <p className="text-gray-600">
                      {settings?.address || 'Milton Keynes | United Kingdom'}
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-[#30062f] p-3 rounded-full mr-4">
                    <Clock size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-[#30062f]">
                      Business Hours
                    </h3>
                    <div className="text-gray-600">
                      {settings?.businessHours ? (
                        <div dangerouslySetInnerHTML={{ __html: settings.businessHours.replace(/\n/g, '<br>') }} />
                      ) : (
                        <>
                          <p>Monday - Friday: 9:00 AM - 5:00 PM</p>
                          <p>Weekends: By appointment only</p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <ConsultationSection onBookConsultation={() => setIsConsultationModalOpen(true)} />
            </div>
          </div>
        </div>
      </section>
      {/* Map Section */}
      <section className="py-16 bg-[#F9F5FF]">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[#30062f] mb-4">Find Us</h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Visit our office to discuss your event needs in person
            </p>
          </div>
          <div className="h-96 bg-[#30062f]/20 rounded-lg overflow-hidden shadow-lg border border-gray-200">
            {/* Google Maps Embed */}
            <iframe
              src={`https://maps.google.com/maps?q=${encodeURIComponent(settings?.address || 'Milton Keynes, United Kingdom')}&t=&z=15&ie=UTF8&iwloc=&output=embed`}
              width="100%"
              height="100%"
              style={{ border: 0 }}
              allowFullScreen={true}
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="BMO Eventz Location"
              className="w-full h-full"
              onError={() => {
                console.log('Map failed to load');
              }}
            />
          </div>
        </div>
      </section>

      {/* Consultation Booking Modal */}
      <ConsultationBooking
        isOpen={isConsultationModalOpen}
        onClose={() => setIsConsultationModalOpen(false)}
      />
    </>;
}


