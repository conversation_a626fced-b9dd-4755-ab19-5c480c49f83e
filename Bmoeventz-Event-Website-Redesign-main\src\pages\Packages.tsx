import { Link } from 'react-router-dom';
import { Check, Star, Crown, PartyPopper } from 'lucide-react';
import { usePackages } from '../hooks/useContentful';

// Icon mapping for dynamic icons
const iconMap = {
  check: Check,
  star: Star,
  crown: Crown,
  'party-popper': PartyPopper,
};

export function Packages() {
  const { packages, loading, error } = usePackages();

  // Fallback packages for testing
  const fallbackPackages = [
    {
      id: 1,
      name: "Essentials Package",
      price: "from £950",
      description: "Perfect for small meetings, internal briefings, or team moments.",
      icon: "check",
      color: "from-[#30062f] to-[#4a0a4b]",
      popular: false,
      active: true,
      features: [
        "Event-day coordination (up to 5 hours)",
        "Basic styling (table linens, branded signage)",
        "Vendor timeline management",
        "1 pre-event call with client"
      ]
    },
    {
      id: 2,
      name: "Professional Package",
      price: "from £1,850",
      description: "Ideal for client-facing events, conferences, or networking mixers.",
      icon: "star",
      color: "from-[#4a0a4b] to-[#6b1b7f]",
      popular: true,
      active: true,
      features: [
        "Full event planning + on-the-day coordination",
        "Venue sourcing & supplier management",
        "Styled signage & branding",
        "Guest list support & RSVPs"
      ]
    }
  ];

  // Use fallback data if Contentful fails
  const displayPackages = packages.length > 0 ? packages : fallbackPackages;

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative w-full bg-cover bg-center py-20 md:py-28 bg-[#30062f]" style={{
        backgroundImage: `linear-gradient(rgba(48, 6, 47, 0.4), rgba(48, 6, 47, 0.4)), url('https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`,
        backgroundPosition: 'center',
        backgroundSize: 'cover'
      }}>
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Event Packages
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            Choose the perfect package for your event needs. From intimate meetings to large-scale events.
          </p>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-16 md:py-24">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-6">
              Our Event Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Tailored solutions for every event, from small team meetings to executive events.
            </p>
          </div>

          {loading && (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#30062f]"></div>
              <p className="mt-2 text-gray-600">Loading packages...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-600">Error loading packages: {error}</p>
              <p className="text-gray-600 mt-2">Displaying fallback content...</p>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
            {packages.filter(pkg => pkg.active).map((pkg, index) => {
              const IconComponent = iconMap[pkg.icon] || Check;
              return (
              <div
                key={index}
                className={`relative bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col ${
                  pkg.popular ? 'ring-2 ring-[#c3bc70] ring-opacity-60' : ''
                }`}
                style={{ overflow: 'visible' }}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-30">
                    <div className="bg-[#c3bc70] text-[#30062f] px-6 py-2.5 rounded-full text-sm font-extrabold shadow-2xl border-3 border-white uppercase tracking-wide">
                      MOST POPULAR
                    </div>
                  </div>
                )}

                <div className={`bg-gradient-to-r ${pkg.color || 'from-[#30062f] to-[#4a0a4b]'} p-6 text-white text-center`}>
                  <div className="bg-white bg-opacity-20 p-3 rounded-full mx-auto mb-3 w-14 h-14 flex items-center justify-center">
                    <IconComponent size={24} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">{pkg.name}</h3>
                  <div className="text-2xl font-bold mb-2">{pkg.price}</div>
                  <p className="text-white/80 text-sm leading-relaxed">{pkg.description}</p>
                </div>

                <div className="p-6 flex-grow flex flex-col">
                  <ul className="space-y-3 mb-6 flex-grow">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <Check size={20} className="text-[#c3bc70] mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Link
                    to="/contact"
                    className={`w-full block text-center py-3 px-6 rounded-md font-medium transition-colors ${
                      pkg.popular
                        ? 'bg-[#a0224b] text-white hover:bg-[#8a1e40]'
                        : 'bg-[#30062f] text-white hover:bg-[#4a0a4b]'
                    }`}
                    onClick={scrollToTop}
                  >
                    Get Started
                  </Link>
                </div>
              </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-[#30062f] to-[#4a0a4b]">
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Plan Your Corporate Event?
          </h2>
          <p className="text-lg text-white/80 mb-8 max-w-3xl mx-auto">
            Let's discuss your event requirements and create a customized package that fits your needs and budget.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Link
              to="/contact"
              className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors"
              onClick={scrollToTop}
            >
              Request a Quote
            </Link>
            <Link 
              to="/events-planning" 
              className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-md font-medium hover:bg-white hover:text-[#30062f] transition-colors"
            >
              Learn Our Process
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}


