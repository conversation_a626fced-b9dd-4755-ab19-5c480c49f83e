import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useCart } from '../contexts/CartContext';
import { X, CreditCard, Lock, CheckCircle } from 'lucide-react';

// Initialize Stripe (you'll need to add your publishable key to .env)
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '');

interface CheckoutFormProps {
  onClose: () => void;
}

function CheckoutForm({ onClose }: CheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const { state: cartState, clearCart } = useCart();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [succeeded, setSucceeded] = useState(false);

  // Customer details
  const [customerDetails, setCustomerDetails] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    eventDate: '',
    eventType: '',
    specialRequests: ''
  });

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setProcessing(true);
    setError(null);

    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      setError('Card element not found');
      setProcessing(false);
      return;
    }

    try {
      // Create payment intent on your backend
      const response = await fetch('/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: Math.round(cartState.totalAmount * 100), // Convert to cents
          currency: 'gbp',
          items: cartState.items,
          customer: customerDetails,
        }),
      });

      const { client_secret } = await response.json();

      // Confirm payment
      const result = await stripe.confirmCardPayment(client_secret, {
        payment_method: {
          card: cardElement,
          billing_details: {
            name: customerDetails.name,
            email: customerDetails.email,
            phone: customerDetails.phone,
            address: {
              line1: customerDetails.address,
              city: customerDetails.city,
              postal_code: customerDetails.postalCode,
              country: 'GB',
            },
          },
        },
      });

      if (result.error) {
        setError(result.error.message || 'Payment failed');
      } else {
        setSucceeded(true);
        clearCart();
        // You can add additional success handling here
      }
    } catch (err) {
      setError('Payment processing failed. Please try again.');
      console.error('Payment error:', err);
    }

    setProcessing(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setCustomerDetails({
      ...customerDetails,
      [e.target.name]: e.target.value
    });
  };

  if (succeeded) {
    return (
      <div className="text-center py-8">
        <CheckCircle size={64} className="text-green-500 mx-auto mb-4" />
        <h3 className="text-2xl font-bold text-green-600 mb-2">Payment Successful!</h3>
        <p className="text-gray-600 mb-4">
          Thank you for your rental booking. We'll contact you shortly to confirm delivery details.
        </p>
        <button
          onClick={onClose}
          className="bg-[#a0224b] text-white px-6 py-2 rounded-md hover:bg-[#8a1e40] transition-colors"
        >
          Close
        </button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Customer Details */}
      <div>
        <h3 className="text-lg font-semibold text-[#30062f] mb-4">Contact Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input
            type="text"
            name="name"
            placeholder="Full Name *"
            value={customerDetails.name}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
          />
          <input
            type="email"
            name="email"
            placeholder="Email Address *"
            value={customerDetails.email}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
          />
          <input
            type="tel"
            name="phone"
            placeholder="Phone Number *"
            value={customerDetails.phone}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
          />
          <input
            type="date"
            name="eventDate"
            placeholder="Event Date"
            value={customerDetails.eventDate}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
          />
        </div>
      </div>

      {/* Address */}
      <div>
        <h3 className="text-lg font-semibold text-[#30062f] mb-4">Delivery Address</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input
            type="text"
            name="address"
            placeholder="Street Address *"
            value={customerDetails.address}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f] md:col-span-2"
          />
          <input
            type="text"
            name="city"
            placeholder="City *"
            value={customerDetails.city}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
          />
          <input
            type="text"
            name="postalCode"
            placeholder="Postal Code *"
            value={customerDetails.postalCode}
            onChange={handleInputChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
          />
        </div>
      </div>

      {/* Event Details */}
      <div>
        <input
          type="text"
          name="eventType"
          placeholder="Event Type (e.g., Wedding, Corporate Event)"
          value={customerDetails.eventType}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f] mb-4"
        />
        <textarea
          name="specialRequests"
          placeholder="Special requests or delivery instructions..."
          value={customerDetails.specialRequests}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f] resize-none"
        />
      </div>

      {/* Payment Details */}
      <div>
        <h3 className="text-lg font-semibold text-[#30062f] mb-4 flex items-center">
          <Lock size={20} className="mr-2" />
          Payment Information
        </h3>
        <div className="p-4 border border-gray-300 rounded-md bg-gray-50">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
              },
            }}
          />
        </div>
      </div>

      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}

      {/* Order Summary */}
      <div className="bg-gray-50 p-4 rounded-md">
        <h4 className="font-semibold text-[#30062f] mb-2">Order Summary</h4>
        <div className="space-y-1 text-sm">
          {cartState.items.map((item) => (
            <div key={item.id} className="flex justify-between">
              <span>{item.name} (×{item.quantity}, {item.rentalDays} days)</span>
              <span>£{item.totalPrice.toFixed(2)}</span>
            </div>
          ))}
          <div className="border-t pt-2 flex justify-between font-bold">
            <span>Total:</span>
            <span>£{cartState.totalAmount.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={!stripe || processing}
        className="w-full bg-[#a0224b] text-white py-3 px-4 rounded-md font-medium hover:bg-[#8a1e40] transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2"
      >
        <CreditCard size={20} />
        {processing ? 'Processing...' : `Pay £${cartState.totalAmount.toFixed(2)}`}
      </button>
    </form>
  );
}

interface StripeCheckoutProps {
  isOpen: boolean;
  onClose: () => void;
}

export function StripeCheckout({ isOpen, onClose }: StripeCheckoutProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-[#30062f]">Secure Checkout</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <Elements stripe={stripePromise}>
            <CheckoutForm onClose={onClose} />
          </Elements>
        </div>
      </div>
    </div>
  );
}
