import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Menu, X, ShoppingCart } from 'lucide-react';
import { useCart } from '../contexts/CartContext';
export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { state: cartState, openCart } = useCart();
  const closeMenu = () => {
    setIsMenuOpen(false);
  };
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  return <header className="bg-[#30062f] text-white py-4 px-6 md:px-12">
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row md:items-center">
        <div className="flex justify-between items-center w-full md:w-auto">
          <Link to="/" className="flex items-center" onClick={scrollToTop}>
            <img
              src="/logo.png"
              alt="BMO Eventz Logo"
              className="h-16 mr-1"
              onError={(e) => {
                console.log('Logo failed to load, trying fallback');
                e.currentTarget.src = "/image.png";
              }}
              onLoad={() => console.log('Logo loaded successfully')}
            />
            <span className="text-xl font-bold text-[#f2dc79] drop-shadow-sm">BMO EVENTZ</span>
          </Link>
          {/* Mobile Menu Button */}
          <button className="md:hidden text-white" onClick={() => setIsMenuOpen(!isMenuOpen)}>
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
        {/* Desktop Navigation - Centered */}
        <div className="hidden md:flex flex-grow justify-center items-center">
          <nav className="flex space-x-8">
            <Link to="/" className="hover:text-[#a0224b] transition-colors" onClick={scrollToTop}>
              Home
            </Link>
            <Link to="/about" className="hover:text-[#a0224b] transition-colors" onClick={scrollToTop}>
              About
            </Link>
            <Link to="/services" className="hover:text-[#a0224b] transition-colors" onClick={scrollToTop}>
              Services
            </Link>
            <Link to="/events-planning" className="hover:text-[#a0224b] transition-colors" onClick={scrollToTop}>
              Event Planning
            </Link>
            <Link to="/gallery" className="hover:text-[#a0224b] transition-colors" onClick={scrollToTop}>
              Gallery
            </Link>
            <Link to="/contact" className="hover:text-[#a0224b] transition-colors" onClick={scrollToTop}>
              Contact
            </Link>
            <Link to="/contact" className="bg-[#a0224b] text-white px-4 py-1 rounded-md hover:bg-[#8a1e40] transition-colors" onClick={scrollToTop}>
              Get in Touch
            </Link>
          </nav>
        </div>

        {/* Cart Icon - Desktop */}
        <div className="hidden md:flex items-center">
          <button
            onClick={openCart}
            className="relative p-2 hover:text-[#a0224b] transition-colors"
          >
            <ShoppingCart size={24} />
            {cartState.totalItems > 0 && (
              <span className="absolute -top-1 -right-1 bg-[#a0224b] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {cartState.totalItems}
              </span>
            )}
          </button>
        </div>
      </div>
      {/* Mobile Navigation */}
      {isMenuOpen && <div className="md:hidden bg-[#30062f] mt-4 px-6 py-4">
          <nav className="flex flex-col space-y-4">
            <Link to="/" className="text-white hover:text-[#a0224b] transition-colors" onClick={() => {
          closeMenu();
          scrollToTop();
        }}>
              Home
            </Link>
            <Link to="/about" className="text-white hover:text-[#a0224b] transition-colors" onClick={() => {
          closeMenu();
          scrollToTop();
        }}>
              About
            </Link>
            <Link to="/services" className="text-white hover:text-[#a0224b] transition-colors" onClick={() => {
          closeMenu();
          scrollToTop();
        }}>
              Services
            </Link>
            <Link to="/events-planning" className="text-white hover:text-[#a0224b] transition-colors" onClick={() => {
          closeMenu();
          scrollToTop();
        }}>
              Event Planning
            </Link>
            <Link to="/gallery" className="text-white hover:text-[#a0224b] transition-colors" onClick={() => {
          closeMenu();
          scrollToTop();
        }}>
              Gallery
            </Link>
            <Link to="/contact" className="text-white hover:text-[#a0224b] transition-colors" onClick={() => {
          closeMenu();
          scrollToTop();
        }}>
              Contact
            </Link>
            <Link to="/contact" className="bg-[#a0224b] text-white px-4 py-2 rounded-md hover:bg-[#8a1e40] transition-colors w-full text-center" onClick={() => {
          closeMenu();
          scrollToTop();
        }}>
              Get in Touch
            </Link>

            {/* Cart Button - Mobile */}
            <button
              onClick={() => {
                closeMenu();
                openCart();
              }}
              className="flex items-center justify-between text-white hover:text-[#a0224b] transition-colors py-2"
            >
              <span className="flex items-center gap-2">
                <ShoppingCart size={20} />
                Cart
              </span>
              {cartState.totalItems > 0 && (
                <span className="bg-[#a0224b] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartState.totalItems}
                </span>
              )}
            </button>
          </nav>
        </div>}
    </header>;
}