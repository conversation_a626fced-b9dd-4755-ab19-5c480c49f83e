import { gql } from '@apollo/client';

// Query for all services
export const GET_SERVICES = gql`
  query GetServices {
    services {
      nodes {
        id
        title
        content
        slug
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
        serviceFields {
          price
          description
          features
        }
      }
    }
  }
`;

// Query for all events
export const GET_EVENTS = gql`
  query GetEvents {
    events {
      nodes {
        id
        title
        content
        slug
        date
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
        eventFields {
          eventDate
          location
          price
          capacity
        }
      }
    }
  }
`;

// Query for gallery items
export const GET_GALLERY = gql`
  query GetGallery {
    galleryItems {
      nodes {
        id
        title
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
        galleryFields {
          category
          description
        }
      }
    }
  }
`;

// Query for pages (About, Contact, etc.)
export const GET_PAGE_BY_SLUG = gql`
  query GetPageBySlug($slug: String!) {
    pageBy(slug: $slug) {
      id
      title
      content
      slug
      featuredImage {
        node {
          sourceUrl
          altText
        }
      }
    }
  }
`;

// Query for posts/blog
export const GET_POSTS = gql`
  query GetPosts($first: Int = 10) {
    posts(first: $first) {
      nodes {
        id
        title
        excerpt
        content
        slug
        date
        author {
          node {
            name
          }
        }
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
      }
    }
  }
`;

// Mutation for contact form submission
export const SUBMIT_CONTACT_FORM = gql`
  mutation SubmitContactForm($input: ContactFormInput!) {
    submitContactForm(input: $input) {
      success
      message
    }
  }
`;
