import { createClient } from 'contentful';

// Contentful client configuration
const client = createClient({
  space: import.meta.env.VITE_CONTENTFUL_SPACE_ID,
  accessToken: import.meta.env.VITE_CONTENTFUL_ACCESS_TOKEN,
});

// Services API
export const contentfulService = {
  // Get all services
  async getServices() {
    try {
      const response = await client.getEntries({
        content_type: 'serviceContentType',
        order: 'fields.order',
      });
      return response.items.map(item => ({
        id: item.sys.id,
        title: item.fields.title,
        description: item.fields.description,
        features: item.fields.features || [],
        icon: item.fields.icon,
        active: item.fields.active !== false,
        order: item.fields.order || 0,
      }));
    } catch (error) {
      console.error('Error fetching services:', error);
      return [];
    }
  },

  // Get all packages
  async getPackages() {
    try {
      const response = await client.getEntries({
        content_type: 'packageContentType',
        order: 'fields.order',
      });
      return response.items.map(item => ({
        id: item.sys.id,
        name: item.fields.name,
        price: item.fields.price,
        description: item.fields.description,
        features: item.fields.features || [],
        popular: item.fields.popular || false,
        active: item.fields.active !== false,
        color: item.fields.color,
        icon: item.fields.icon,
        order: item.fields.order || 0,
      }));
    } catch (error) {
      console.error('Error fetching packages:', error);
      return [];
    }
  },

  // Get gallery images
  async getGalleryImages() {
    try {
      const response = await client.getEntries({
        content_type: 'galleryImage',
        order: '-sys.createdAt',
      });
      return response.items.map(item => ({
        id: item.sys.id,
        image: item.fields.image?.fields?.file?.url ? `https:${item.fields.image.fields.file.url}` : '',
        title: item.fields.title,
        category: item.fields.category,
        featured: item.fields.featured || false,
        eventDate: item.fields.eventDate,
      }));
    } catch (error) {
      console.error('Error fetching gallery images:', error);
      return [];
    }
  },

  // Get site settings
  async getSiteSettings() {
    try {
      const response = await client.getEntries({
        content_type: 'siteSettingsContentType',
        limit: 1,
      });
      return response.items.length > 0 ? {
        id: response.items[0].sys.id,
        settingName: response.items[0].fields.settingName,
        businessName: response.items[0].fields.businessName,
        phoneNumber: response.items[0].fields.phoneNumber,
        emailAddress: response.items[0].fields.emailAddress,
        address: response.items[0].fields.address,
        businessHours: response.items[0].fields.businessHours,
        socialMediaLinks: response.items[0].fields.socialMediaLinks || {},
        brandColor: response.items[0].fields.brandColor,
        active: response.items[0].fields.active,
      } : null;
    } catch (error) {
      console.error('Error fetching site settings:', error);
      return null;
    }
  },

  // Get page content
  async getPageContent(pageId) {
    try {
      const response = await client.getEntries({
        content_type: 'pageContent',
        'fields.pageId': pageId,
      });
      if (response.items.length > 0) {
        const item = response.items[0];
        return {
          id: item.sys.id,
          pageId: item.fields.pageId,
          title: item.fields.title,
          content: item.fields.content,
          heroTitle: item.fields.heroTitle,
          heroSubtitle: item.fields.heroSubtitle,
          sections: item.fields.sections || [],
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching page content:', error);
      return null;
    }
  },

  // Get site settings
  async getSiteSettings() {
    try {
      const response = await client.getEntries({
        content_type: 'siteSettings',
        limit: 1,
      });
      if (response.items.length > 0) {
        const item = response.items[0];
        return {
          id: item.sys.id,
          siteName: item.fields.siteName,
          contactEmail: item.fields.contactEmail,
          contactPhone: item.fields.contactPhone,
          businessHours: item.fields.businessHours,
          socialLinks: item.fields.socialLinks || {},
          address: item.fields.address,
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching site settings:', error);
      return null;
    }
  },

  // Get social media links
  async getSocialMediaLinks() {
    try {
      const response = await client.getEntries({
        content_type: 'socialMediaContentType',
        order: 'fields.order',
      });
      return response.items.map(item => ({
        id: item.sys.id,
        platform: item.fields.platform,
        url: item.fields.url,
        icon: item.fields.icon,
        displayText: item.fields.displayText,
        order: item.fields.order,
        active: item.fields.active,
      }));
    } catch (error) {
      console.error('Error fetching social media links:', error);
      return [];
    }
  },

  // Get hero/banner content
  async getHeroBanners() {
    try {
      const response = await client.getEntries({
        content_type: 'heroBannerContentType',
        order: 'fields.order',
      });
      return response.items.map(item => ({
        id: item.sys.id,
        title: item.fields.title,
        subtitle: item.fields.subtitle,
        backgroundImage: item.fields.backgroundImage?.fields?.file?.url ? `https:${item.fields.backgroundImage.fields.file.url}` : '',
        buttonText: item.fields.buttonText,
        buttonLink: item.fields.buttonLink,
        pageLocation: item.fields.pageLocation,
        active: item.fields.active,
        order: item.fields.order,
      }));
    } catch (error) {
      console.error('Error fetching hero banners:', error);
      return [];
    }
  },

  // Get rental items
  async getRentalItems() {
    try {
      const response = await client.getEntries({
        content_type: 'rentalItemsContentType',
        order: 'fields.order',
      });
      return response.items.map(item => {
        const imageUrl = item.fields.image?.fields?.file?.url ? `https:${item.fields.image.fields.file.url}` : '';
        console.log('Processing rental item:', item.fields.name, 'Image URL:', imageUrl);
        return {
          id: item.sys.id,
          name: item.fields.name,
          description: item.fields.description,
          category: item.fields.category,
          price: item.fields.price,
          image: imageUrl,
          availability: item.fields.availability,
          featured: item.fields.featured,
          active: item.fields.active,
          order: item.fields.order,
        };
      });
    } catch (error) {
      console.error('Error fetching rental items:', error);
      return [];
    }
  },
};

export default contentfulService;
