import React, { useState } from 'react';
import { X, Calendar, Clock, CreditCard, User, Mail, Phone, MessageSquare } from 'lucide-react';
import { useGoogleAnalytics } from './GoogleAnalytics';

interface ConsultationBookingProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ConsultationFormData {
  name: string;
  email: string;
  phone: string;
  consultationType: string;
  duration: string;
  preferredDate: string;
  preferredTime: string;
  eventType: string;
  budget: string;
  specificNeeds: string;
}

const consultationOptions = [
  {
    id: 'initial',
    name: 'Initial Consultation',
    duration: '30',
    price: 25,
    description: 'Perfect for getting started and understanding your event vision'
  },
  {
    id: 'detailed',
    name: 'Detailed Planning Session',
    duration: '60',
    price: 45,
    description: 'Comprehensive planning session with detailed timeline and vendor recommendations'
  },
  {
    id: 'premium',
    name: 'Premium Strategy Session',
    duration: '90',
    price: 65,
    description: 'In-depth consultation with complete event strategy and budget planning'
  }
];

export function ConsultationBooking({ isOpen, onClose }: ConsultationBookingProps) {
  const { trackEvent, trackFormSubmit } = useGoogleAnalytics();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [selectedConsultation, setSelectedConsultation] = useState(consultationOptions[0]);
  
  const [formData, setFormData] = useState<ConsultationFormData>({
    name: '',
    email: '',
    phone: '',
    consultationType: 'initial',
    duration: '30',
    preferredDate: '',
    preferredTime: '',
    eventType: '',
    budget: '',
    specificNeeds: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleConsultationSelect = (consultation: typeof consultationOptions[0]) => {
    setSelectedConsultation(consultation);
    setFormData(prev => ({
      ...prev,
      consultationType: consultation.id,
      duration: consultation.duration
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Submit to Formsubmit
      const formSubmitData = new FormData();
      formSubmitData.append('_subject', `New Consultation Booking - ${selectedConsultation.name}`);
      formSubmitData.append('_template', 'table');
      formSubmitData.append('_captcha', 'false');
      formSubmitData.append('name', formData.name);
      formSubmitData.append('email', formData.email);
      formSubmitData.append('phone', formData.phone);
      formSubmitData.append('consultation_type', selectedConsultation.name);
      formSubmitData.append('duration', `${selectedConsultation.duration} minutes`);
      formSubmitData.append('price', `£${selectedConsultation.price}`);
      formSubmitData.append('preferred_date', formData.preferredDate);
      formSubmitData.append('preferred_time', formData.preferredTime);
      formSubmitData.append('event_type', formData.eventType);
      formSubmitData.append('budget', formData.budget);
      formSubmitData.append('specific_needs', formData.specificNeeds);

      const response = await fetch('https://formsubmit.co/<EMAIL>', {
        method: 'POST',
        body: formSubmitData
      });

      if (response.ok) {
        // Track successful form submission
        trackFormSubmit('consultation_booking', 'consultation-booking-form');
        trackEvent('consultation_booking_submitted', {
          consultation_type: selectedConsultation.id,
          duration: selectedConsultation.duration,
          price: selectedConsultation.price
        });

        // Move to payment step - booking NOT confirmed until payment
        setCurrentStep(3);
      } else {
        throw new Error('Form submission failed');
      }
    } catch (error) {
      console.error('Error submitting consultation booking:', error);
      alert('There was an error submitting your booking. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePayment = () => {
    // Track payment initiation
    trackEvent('consultation_payment_initiated', {
      consultation_type: selectedConsultation.id,
      amount: selectedConsultation.price
    });

    // Here you would integrate with Stripe payment processor
    // For now, we'll show that payment integration is required
    alert(`Stripe payment integration required. Amount: £${selectedConsultation.price}. Booking will be confirmed only after successful payment.`);

    // TODO: Replace with actual Stripe integration
    // After successful Stripe payment, then confirm booking and close modal
    // For now, we don't confirm the booking until payment is implemented
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-2xl w-full h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-[#30062f]">Book a Consultation</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Progress Indicator */}
        <div className="px-6 py-4 bg-gray-50">
          <div className="flex items-center justify-center space-x-4">
            <div className={`flex items-center ${currentStep >= 1 ? 'text-[#30062f]' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                currentStep >= 1 ? 'bg-[#30062f] text-white' : 'bg-gray-200'
              }`}>
                1
              </div>
              <span className="ml-2 text-sm font-medium">Select Package</span>
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className={`flex items-center ${currentStep >= 2 ? 'text-[#30062f]' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                currentStep >= 2 ? 'bg-[#30062f] text-white' : 'bg-gray-200'
              }`}>
                2
              </div>
              <span className="ml-2 text-sm font-medium">Your Details</span>
            </div>
            <div className="w-8 h-px bg-gray-300"></div>
            <div className={`flex items-center ${currentStep >= 3 ? 'text-[#30062f]' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                currentStep >= 3 ? 'bg-[#30062f] text-white' : 'bg-gray-200'
              }`}>
                3
              </div>
              <span className="ml-2 text-sm font-medium">Payment</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {currentStep === 1 && (
            <div>
              <h3 className="text-lg font-semibold text-[#30062f] mb-4">Choose Your Consultation Package</h3>
              <div className="space-y-4">
                {consultationOptions.map((option) => (
                  <div
                    key={option.id}
                    className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                      selectedConsultation.id === option.id
                        ? 'border-[#30062f] bg-[#30062f]/5'
                        : 'border-gray-200 hover:border-[#30062f]/50'
                    }`}
                    onClick={() => handleConsultationSelect(option)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-semibold text-[#30062f]">{option.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                        <div className="flex items-center mt-2 text-sm text-gray-500">
                          <Clock size={16} className="mr-1" />
                          {option.duration} minutes
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-[#30062f]">£{option.price}</div>
                        <div className="text-sm text-gray-500">one-time</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={() => setCurrentStep(2)}
                className="w-full mt-6 bg-[#30062f] text-white py-3 px-4 rounded-lg font-medium hover:bg-[#4a0a4b] transition-colors"
              >
                Continue to Details
              </button>
            </div>
          )}

          {currentStep === 2 && (
            <form onSubmit={handleSubmit}>
              <h3 className="text-lg font-semibold text-[#30062f] mb-4">Your Information</h3>
              
              {/* Selected Package Summary */}
              <div className="bg-[#30062f]/5 border border-[#30062f]/20 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-[#30062f]">{selectedConsultation.name}</h4>
                    <p className="text-sm text-gray-600">{selectedConsultation.duration} minutes</p>
                  </div>
                  <div className="text-xl font-bold text-[#30062f]">£{selectedConsultation.price}</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <User size={16} className="inline mr-1" />
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Mail size={16} className="inline mr-1" />
                    Email Address *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Phone size={16} className="inline mr-1" />
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Type
                  </label>
                  <select
                    name="eventType"
                    value={formData.eventType}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                  >
                    <option value="">Select event type</option>
                    <option value="wedding">Wedding</option>
                    <option value="corporate">Corporate Event</option>
                    <option value="birthday">Birthday Party</option>
                    <option value="anniversary">Anniversary</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar size={16} className="inline mr-1" />
                    Preferred Date
                  </label>
                  <input
                    type="date"
                    name="preferredDate"
                    value={formData.preferredDate}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Clock size={16} className="inline mr-1" />
                    Preferred Time
                  </label>
                  <select
                    name="preferredTime"
                    value={formData.preferredTime}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                  >
                    <option value="">Select time</option>
                    <option value="09:00">9:00 AM</option>
                    <option value="10:00">10:00 AM</option>
                    <option value="11:00">11:00 AM</option>
                    <option value="12:00">12:00 PM</option>
                    <option value="13:00">1:00 PM</option>
                    <option value="14:00">2:00 PM</option>
                    <option value="15:00">3:00 PM</option>
                    <option value="16:00">4:00 PM</option>
                    <option value="17:00">5:00 PM</option>
                  </select>
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Budget Range
                </label>
                <select
                  name="budget"
                  value={formData.budget}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
                >
                  <option value="">Select budget range</option>
                  <option value="under-1000">Under £1,000</option>
                  <option value="1000-5000">£1,000 - £5,000</option>
                  <option value="5000-10000">£5,000 - £10,000</option>
                  <option value="10000-20000">£10,000 - £20,000</option>
                  <option value="over-20000">Over £20,000</option>
                </select>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <MessageSquare size={16} className="inline mr-1" />
                  Specific Needs or Questions
                </label>
                <textarea
                  name="specificNeeds"
                  value={formData.specificNeeds}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f] resize-none"
                  placeholder="Tell us about your event vision, specific requirements, or any questions you have..."
                />
              </div>

              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => setCurrentStep(1)}
                  className="flex-1 bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-300 transition-colors"
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1 bg-[#30062f] text-white py-3 px-4 rounded-lg font-medium hover:bg-[#4a0a4b] transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Submitting...' : 'Continue to Payment'}
                </button>
              </div>
            </form>
          )}

          {currentStep === 3 && (
            <div className="text-center">
              <div className="mb-6">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CreditCard size={32} className="text-orange-600" />
                </div>
                <h3 className="text-lg font-semibold text-[#30062f] mb-2">Payment Required</h3>
                <p className="text-gray-600">Complete your payment to confirm your consultation booking.</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-semibold text-[#30062f] mb-2">Payment Summary</h4>
                <div className="flex justify-between items-center">
                  <span>{selectedConsultation.name} ({selectedConsultation.duration} min)</span>
                  <span className="font-bold">£{selectedConsultation.price}</span>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <p className="text-yellow-800 text-sm">
                  <strong>Important:</strong> Your consultation booking will only be confirmed after successful payment through our secure Stripe payment system.
                </p>
              </div>

              <button
                onClick={handlePayment}
                className="w-full bg-[#a0224b] text-white py-3 px-4 rounded-md font-medium hover:bg-[#8a1e40] transition-colors flex items-center justify-center gap-2"
              >
                <CreditCard size={20} />
                Proceed to Secure Payment
              </button>

              <p className="text-xs text-gray-500 mt-4">
                Secure payment processing via Stripe. Your booking will be confirmed upon successful payment.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
