# Contentful Integration Guide

## 🎯 Overview
Your website now has hooks and services ready for the following Contentful content types:

## ✅ Currently Active Content Types

### 1. Services (`serviceContentType`)
- **Location**: Services page
- **Status**: ✅ Active and working
- **Fields**: title, description, features, icon, active, order

### 2. Packages (`packageContentType`)
- **Location**: Packages page
- **Status**: ✅ Active and working
- **Fields**: name, price, description, features, popular, active, color, icon, order

### 3. Gallery Images (`galleryImageContentType`)
- **Location**: Gallery page
- **Status**: ✅ Ready to use (create content type in Contentful)
- **Fields**: title, image, category, featured, eventDate, description, active, order

### 4. Site Settings (`siteSettingsContentType`)
- **Location**: Contact page
- **Status**: ✅ Active and working
- **Fields**: settingName, businessName, phoneNumber, emailAddress, address, businessHours, active

## 🚀 Ready for Future Use

### 5. Rental Items (`rental items content type`)
- **Location**: Rentals page
- **Status**: ✅ Active and working
- **Fields**: name, description, category, price, image, availability, featured, active, order

### 6. Social Media Links (`socialMediaContentType`)
- **Location**: Footer (ready to activate)
- **Status**: 🔧 Code ready, needs activation
- **Fields**: platform, url, icon, displayText, order, active

**To activate:**
1. Uncomment the import in `src/components/Footer.tsx`
2. Uncomment the hook usage
3. Replace static social links with dynamic ones

### 7. Hero/Banner Content (`heroBannerContentType`)
- **Location**: Homepage Hero, other page banners
- **Status**: 🔧 Code ready, needs activation
- **Fields**: title, subtitle, backgroundImage, buttonText, buttonLink, pageLocation, active, order

**To activate:**
1. Uncomment the import in `src/components/Hero.tsx`
2. Uncomment the hook usage
3. Replace static hero content with dynamic content

## 📋 How to Use the Ready Content Types

### Social Media Links Example:
```javascript
// In Footer.tsx
import { useSocialMediaLinks } from '../hooks/useContentful';

export function Footer() {
  const { socialLinks, loading, error } = useSocialMediaLinks();
  
  return (
    <div className="flex space-x-4">
      {socialLinks.filter(link => link.active).map((link) => (
        <a key={link.id} href={link.url} className="text-white hover:text-[#a0224b]">
          {/* Use link.icon to render appropriate icon */}
          {link.displayText}
        </a>
      ))}
    </div>
  );
}
```

### Hero/Banner Content Example:
```javascript
// In Hero.tsx
import { useHeroBanners } from '../hooks/useContentful';

export function Hero() {
  const { heroBanners, loading, error } = useHeroBanners('Homepage');
  const heroContent = heroBanners[0]; // Get first active hero for homepage
  
  return (
    <section style={{
      backgroundImage: heroContent?.backgroundImage 
        ? `url('${heroContent.backgroundImage}')`
        : `url('/hero-bg.PNG')`
    }}>
      <h1>{heroContent?.title || 'Purpose-Driven Event Planning with Flair'}</h1>
      <p>{heroContent?.subtitle || 'Default subtitle...'}</p>
      <button>{heroContent?.buttonText || 'Plan Your Event'}</button>
    </section>
  );
}
```

## 🎯 Benefits for Your Client

### Current Benefits:
- ✅ Update services without developer
- ✅ Manage packages and pricing
- ✅ Add/remove gallery images
- ✅ Update contact information

### Future Benefits (when activated):
- 🔧 Update social media links
- 🔧 Change hero banners and promotional content
- 🔧 Manage homepage messaging

## 📞 Support
All the code infrastructure is in place. When your client wants to activate the additional features, simply uncomment the relevant code sections and they'll work immediately with the Contentful content types you've already created.
