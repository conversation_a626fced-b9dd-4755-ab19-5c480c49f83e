import React from 'react';
import { Phone, Mail, MapPin, Instagram, Facebook, Twitter, Youtube } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useGoogleAnalytics } from './GoogleAnalytics';
// import { useSocialMediaLinks } from '../hooks/useContentful';
export function Footer() {
  // Future: Uncomment to use dynamic social media links from Contentful
  // const { socialLinks, loading, error } = useSocialMediaLinks();
  const { trackEvent } = useGoogleAnalytics();

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  return <footer className="bg-[#220419] text-white py-12">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <Link to="/" className="flex items-center mb-4" onClick={scrollToTop}>
              <img
                src="/logo.png"
                alt="BMO Eventz Logo"
                className="h-14 mr-1"
                onError={(e) => {
                  console.log('Footer logo failed to load, trying fallback');
                  e.currentTarget.src = "/image.png";
                }}
                onLoad={() => console.log('Footer logo loaded successfully')}
              />
              <span className="text-xl font-bold text-[#f2dc79] drop-shadow-sm">
                BMO EVENTZ
              </span>
            </Link>
            <p className="text-gray-400 mb-4">
              Purpose-driven event planning with flair. We handle the stress so
              you can enjoy the moment.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://www.instagram.com/bmoeventz/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-[#a0224b] transition-colors"
                title="Follow us on Instagram"
                onClick={() => trackEvent('social_media_click', { platform: 'instagram' })}
              >
                <Instagram size={20} />
              </a>
              <a
                href="https://www.facebook.com/profile.php?id=61572094104393"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-[#a0224b] transition-colors"
                title="Like us on Facebook"
                onClick={() => trackEvent('social_media_click', { platform: 'facebook' })}
              >
                <Facebook size={20} />
              </a>
              <a
                href="https://www.youtube.com/shorts/Lw9TtvW0cuA?feature=share"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-[#a0224b] transition-colors"
                title="Watch our work on YouTube"
                onClick={() => trackEvent('social_media_click', { platform: 'youtube' })}
              >
                <Youtube size={20} />
              </a>
            </div>
          </div>
          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/services" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Services
                </Link>
              </li>
              <li>
                <Link to="/events-planning" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Event Planning
                </Link>
              </li>
              <li>
                <Link to="/gallery" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Gallery
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Contact
                </Link>
              </li>
              <li>
                <Link to="/banner" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Marketing Materials
                </Link>
              </li>
              <li>
                <Link to="/gallery" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Pictures
                </Link>
              </li>
            </ul>
          </div>
          {/* Services */}
          <div>
            <h3 className="text-lg font-bold mb-4">Our Services</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/events-planning" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Event Planning and Coordination
                </Link>
              </li>
              <li>
                <Link to="/services" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Venue Styling
                </Link>
              </li>
              <li>
                <Link to="/rentals" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Event Rentals
                </Link>
              </li>
              <li>
                <Link to="/packages" className="text-gray-400 hover:text-white" onClick={scrollToTop}>
                  Our Packages
                </Link>
              </li>
            </ul>
          </div>
          {/* Contact */}
          <div>
            <h3 className="text-lg font-bold mb-4">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <Phone size={18} className="mr-2 mt-1 text-[#a0224b]" />
                <span className="text-gray-400">+44 01908739814</span>
              </li>
              <li className="flex items-start">
                <Mail size={18} className="mr-2 mt-1 text-[#a0224b]" />
                <span className="text-gray-400"><EMAIL></span>
              </li>
              <li className="flex items-start">
                <MapPin size={18} className="mr-2 mt-1 text-[#a0224b]" />
                <span className="text-gray-400">
                  Milton Keynes | United Kingdom
                </span>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-12 pt-6 text-center text-gray-500">
          <p>
            &copy; {new Date().getFullYear()} BMO Eventz. All rights reserved.
          </p>
        </div>
      </div>
    </footer>;
}