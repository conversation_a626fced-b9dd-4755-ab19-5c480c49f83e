// Enable CORS for your React app
function add_cors_http_header(){
    $allowed_origins = [
        'https://bmoeventz.com',
        'https://www.bmoeventz.com',
        'http://localhost:5173' // Keep for local development
    ];

    $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
    if (in_array($origin, $allowed_origins)) {
        header("Access-Control-Allow-Origin: $origin");
    }

    header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization");
    header("Access-Control-Allow-Credentials: true");
}
add_action('init','add_cors_http_header');

// Register Custom Post Types for GraphQL
function register_custom_post_types() {
    // Services
    register_post_type('services', [
        'labels' => ['name' => 'Services', 'singular_name' => 'Service'],
        'public' => true,
        'show_in_graphql' => true,
        'graphql_single_name' => 'service',
        'graphql_plural_name' => 'services',
        'supports' => ['title', 'editor', 'thumbnail'],
    ]);
    
    // Events
    register_post_type('events', [
        'labels' => ['name' => 'Events', 'singular_name' => 'Event'],
        'public' => true,
        'show_in_graphql' => true,
        'graphql_single_name' => 'event',
        'graphql_plural_name' => 'events',
        'supports' => ['title', 'editor', 'thumbnail'],
    ]);
    
    // Gallery
    register_post_type('gallery_items', [
        'labels' => ['name' => 'Gallery', 'singular_name' => 'Gallery Item'],
        'public' => true,
        'show_in_graphql' => true,
        'graphql_single_name' => 'galleryItem',
        'graphql_plural_name' => 'galleryItems',
        'supports' => ['title', 'thumbnail'],
    ]);
}
add_action('init', 'register_custom_post_types');