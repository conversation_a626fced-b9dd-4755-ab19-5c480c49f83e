import React, { memo } from 'react';
import { Calendar, Palette, Package } from 'lucide-react';
export function WhatWeDo() {
  return <section className="py-16 md:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-4">
            What We Do
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            We create memorable events by combining meticulous planning,
            creative styling, and premium rentals.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Planning & Coordination */}
          <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 flex flex-col items-center text-center hover:shadow-xl transition-shadow">
            <div className="bg-[#30062f] p-4 rounded-full mb-6">
              <Calendar size={32} className="text-white" />
            </div>
            <h3 className="text-xl font-bold text-[#30062f] mb-3">
              Planning & Coordination
            </h3>
            <p className="text-gray-600">
              End-to-end support from initial concept to flawless execution. We
              handle the details, so you can focus on creating exceptional
              experiences.
            </p>
          </div>
          {/* Styling & Atmosphere */}
          <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 flex flex-col items-center text-center hover:shadow-xl transition-shadow">
            <div className="bg-[#30062f] p-4 rounded-full mb-6">
              <Palette size={32} className="text-white" />
            </div>
            <h3 className="text-xl font-bold text-[#30062f] mb-3">
              Styling & Atmosphere
            </h3>
            <p className="text-gray-600">
              Transform ordinary spaces with creative direction, mood boards,
              and detailed setup. We craft elegant venue transformations that
              wow your guests.
            </p>
          </div>
          {/* Rentals */}
          <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 flex flex-col items-center text-center hover:shadow-xl transition-shadow">
            <div className="bg-[#30062f] p-4 rounded-full mb-6">
              <Package size={32} className="text-white" />
            </div>
            <h3 className="text-xl font-bold text-[#30062f] mb-3">Rentals</h3>
            <p className="text-gray-600">
              Choose from our range of functional and styled event essentials.
              From chairs and tables to backdrops and props, we have everything
              you need.
            </p>
          </div>
        </div>
      </div>
    </section>;
}