import React, { useState, memo } from 'react';
import { Link } from 'react-router-dom';
export function Gallery() {
  const categories = ['All', 'Corporate Setups', 'Styled Dinners', 'Community Eventz', 'Kids Parties'];
  const [activeCategory, setActiveCategory] = useState('All');
  const galleryItems = [{
    image: 'https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Corporate Setups',
    title: 'Annual Tech Conference'
  }, {
    image: 'https://images.unsplash.com/photo-1519225421980-715cb0215aed?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Styled Dinners',
    title: 'Executive Gala Dinner'
  }, {
    image: 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Kids Parties',
    title: 'Superhero Birthday Party'
  }, {
    image: 'https://images.unsplash.com/photo-1464207687429-7505649dae38?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Kids Parties',
    title: 'Princess Theme Party'
  }, {
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Kids Parties',
    title: 'Dinosaur Adventure Party'
  }, {
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Kids Parties',
    title: 'Unicorn Magic Party'
  }, {
    image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Kids Parties',
    title: 'Pirate Treasure Hunt'
  }, {
    image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Community Eventz',
    title: 'Community Football Tournament'
  }, {
    image: 'https://images.unsplash.com/photo-1505373877841-8d25f7d46678?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Corporate Setups',
    title: 'Product Launch Event'
  }, {
    image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Styled Dinners',
    title: 'Wedding Reception'
  }, {
    image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Community Eventz',
    title: 'CSR Community Outreach'
  }, {
    image: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Corporate Setups',
    title: 'Industry Awards Ceremony'
  }, {
    image: 'https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Styled Dinners',
    title: 'Charity Fundraising Gala'
  }, {
    image: 'https://images.unsplash.com/photo-1529900748604-07564a03e7a6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'Community Eventz',
    title: 'Sports Day Event'
  }];
  const filteredItems = activeCategory === 'All' ? galleryItems : galleryItems.filter(item => item.category === activeCategory);
  return <>
      {/* Gallery Hero */}
      <section className="relative w-full bg-cover bg-center py-20 md:py-28 bg-[#30062f]" style={{
      backgroundImage: `linear-gradient(rgba(48, 6, 47, 0.4), rgba(48, 6, 47, 0.4)), url('https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`,
      backgroundPosition: 'center',
      backgroundSize: 'cover'
    }}>
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Our Event Portfolio
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            Browse our gallery of successful events showcasing our planning,
            styling, and execution expertise
          </p>
        </div>
      </section>
      {/* Gallery Filter */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map(category => <button key={category} className={`px-4 py-2 rounded-full transition-colors ${activeCategory === category ? 'bg-[#30062f] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`} onClick={() => setActiveCategory(category)}>
                {category}
              </button>)}
          </div>
        </div>
      </section>
      {/* Gallery Grid */}
      <section className="py-16 md:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredItems.map((item, index) => <div key={index} className="group">
                <div className="relative overflow-hidden rounded-lg shadow-lg h-64 mb-4 bg-[#30062f]">
                  <img src={item.image} alt={item.title} className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110" />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#30062f] to-transparent opacity-70"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-4">
                    <span className="text-white/80 text-sm">
                      {item.category}
                    </span>
                    <h3 className="text-white font-medium text-lg">
                      {item.title}
                    </h3>
                  </div>
                </div>
              </div>)}
          </div>
        </div>
      </section>
      {/* CTA */}
      <section className="py-16 bg-gradient-to-r from-[#30062f] to-[#4a0a4b]">
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to create your own memorable event?
          </h2>
          <p className="text-lg text-white/80 mb-8 max-w-3xl mx-auto">
            Let's discuss how we can bring your vision to life with the same
            attention to detail shown in our portfolio.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Link to="/contact" className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors">
              Start Planning Your Event
            </Link>
            <Link to="/services" className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-md font-medium hover:bg-white hover:text-[#30062f] transition-colors">
              Explore Our Services
            </Link>
          </div>
        </div>
      </section>
    </>;
}


