import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AnimatedButton, useSmoothScrollToTop } from './PageTransition';
export function CTA() {
  const navigate = useNavigate();
  const smoothScrollToTop = useSmoothScrollToTop();

  const handleContactClick = () => {
    smoothScrollToTop();
    setTimeout(() => navigate('/contact'), 150);
  };

  const handleRentalsClick = () => {
    smoothScrollToTop();
    setTimeout(() => navigate('/rentals'), 150);
  };
  return <section className="py-16 bg-gradient-to-r from-[#30062f] to-[#4a0a4b]">
      <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
          Let's talk through your event vision today.
        </h2>
        <p className="text-lg text-white/80 mb-8 max-w-3xl mx-auto">
          From corporate gatherings to intimate celebrations, we bring your
          vision to life with precision and flair.
        </p>
        <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
          <AnimatedButton
            onClick={handleContactClick}
            variant="primary"
            size="lg"
            className="bg-[#a0224b] hover:bg-[#8a1e40]"
          >
            Request a Planning Call
          </AnimatedButton>

          <AnimatedButton
            onClick={handleRentalsClick}
            variant="secondary"
            size="lg"
            className="border-white text-white hover:bg-white hover:text-[#30062f]"
          >
            View Rental Catalogue
          </AnimatedButton>
        </div>
      </div>
    </section>;
}