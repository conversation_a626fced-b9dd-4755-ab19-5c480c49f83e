import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Header } from './components/Header';
import { Footer } from './components/Footer';
import { Cart } from './components/Cart';
import { CartProvider } from './contexts/CartContext';
import { GoogleAnalytics } from './components/GoogleAnalytics';
import { PageTransition } from './components/PageTransition';
import { Home } from './pages/Home';
import { About } from './pages/About';
import { Services } from './pages/Services';
import { EventsPlanning } from './pages/EventsPlanning';
import { Gallery } from './pages/Gallery';
import { Contact } from './pages/Contact';
import { Banner } from './pages/Banner';
import { Packages } from './pages/Packages';
import { Rentals } from './pages/Rentals';
export function App() {
  return (
    <HelmetProvider>
      <GoogleAnalytics />
      <CartProvider>
        <BrowserRouter>
          <div className="flex flex-col w-full min-h-screen bg-[#30062f]">
            <Header />
            <main className="flex-grow">
              <PageTransition>
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/about" element={<About />} />
                  <Route path="/services" element={<Services />} />
                  <Route path="/events-planning" element={<EventsPlanning />} />
                  <Route path="/gallery" element={<Gallery />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/banner" element={<Banner />} />
                  <Route path="/packages" element={<Packages />} />
                  <Route path="/rentals" element={<Rentals />} />
                </Routes>
              </PageTransition>
            </main>
            <Footer />
            <Cart />
          </div>
        </BrowserRouter>
      </CartProvider>
    </HelmetProvider>
  );
}