import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { Helmet } from 'react-helmet-async';

interface BreadcrumbItem {
  label: string;
  path: string;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  const location = useLocation();
  
  // Auto-generate breadcrumbs from URL if items not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(segment => segment);
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Home', path: '/' }
    ];

    let currentPath = '';
    pathSegments.forEach(segment => {
      currentPath += `/${segment}`;
      
      // Convert URL segments to readable labels
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      
      breadcrumbs.push({
        label: label === 'Events Planning' ? 'Event Planning' : label,
        path: currentPath
      });
    });

    return breadcrumbs;
  };

  const breadcrumbItems = items || generateBreadcrumbs();

  // Don't show breadcrumbs on home page
  if (location.pathname === '/') {
    return null;
  }

  // Generate structured data for breadcrumbs
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbItems.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.label,
      item: `https://bmoeventz.com${item.path}`
    }))
  };

  return (
    <>
      <Helmet>
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      </Helmet>
      
      <nav 
        className={`bg-gray-50 border-b border-gray-200 ${className}`}
        aria-label="Breadcrumb"
      >
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <ol className="flex items-center space-x-2 py-3 text-sm">
            {breadcrumbItems.map((item, index) => (
              <li key={item.path} className="flex items-center">
                {index > 0 && (
                  <ChevronRight size={16} className="text-gray-400 mx-2" />
                )}
                
                {index === 0 ? (
                  <Link
                    to={item.path}
                    className="flex items-center text-gray-500 hover:text-[#30062f] transition-colors"
                  >
                    <Home size={16} className="mr-1" />
                    {item.label}
                  </Link>
                ) : index === breadcrumbItems.length - 1 ? (
                  <span className="text-[#30062f] font-medium" aria-current="page">
                    {item.label}
                  </span>
                ) : (
                  <Link
                    to={item.path}
                    className="text-gray-500 hover:text-[#30062f] transition-colors"
                  >
                    {item.label}
                  </Link>
                )}
              </li>
            ))}
          </ol>
        </div>
      </nav>
    </>
  );
}

// Pre-defined breadcrumb configurations for specific pages
export const breadcrumbConfigs = {
  services: [
    { label: 'Home', path: '/' },
    { label: 'Services', path: '/services' }
  ],
  rentals: [
    { label: 'Home', path: '/' },
    { label: 'Equipment Rentals', path: '/rentals' }
  ],
  packages: [
    { label: 'Home', path: '/' },
    { label: 'Event Packages', path: '/packages' }
  ],
  about: [
    { label: 'Home', path: '/' },
    { label: 'About Us', path: '/about' }
  ],
  contact: [
    { label: 'Home', path: '/' },
    { label: 'Contact Us', path: '/contact' }
  ],
  gallery: [
    { label: 'Home', path: '/' },
    { label: 'Gallery', path: '/gallery' }
  ],
  'events-planning': [
    { label: 'Home', path: '/' },
    { label: 'Event Planning', path: '/events-planning' }
  ]
};
