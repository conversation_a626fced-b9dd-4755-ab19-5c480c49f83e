import React, { memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { AnimatedButton, useSmoothScrollToTop } from './PageTransition';
// import { useHeroBanners } from '../hooks/useContentful';
export function Hero() {
  // Future: Uncomment to use dynamic hero content from Contentful
  // const { heroBanners, loading, error } = useHeroBanners('Homepage');

  const navigate = useNavigate();
  const smoothScrollToTop = useSmoothScrollToTop();

  const handlePlanEventClick = () => {
    smoothScrollToTop();
    setTimeout(() => navigate('/events-planning'), 150);
  };

  const handleRentalsClick = () => {
    smoothScrollToTop();
    setTimeout(() => navigate('/rentals'), 150);
  };
  return <section className="relative w-full bg-cover bg-center py-24 md:py-32 bg-[#30062f]" style={{
    backgroundImage: `linear-gradient(rgba(48, 6, 47, 0.4), rgba(48, 6, 47, 0.4)), url('/hero-bg.PNG')`,
    backgroundPosition: 'center',
    backgroundSize: 'cover'
  }}>
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="max-w-3xl">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
            Purpose-Driven Event Planning with Flair
          </h1>
          <p className="text-xl md:text-2xl text-white mb-8">
            From first call to final confetti drop — we plan, coordinate, and
            deliver memorable experiences.
          </p>
          <p className="text-white text-lg mb-8 opacity-90">
            We handle the stress so you can enjoy the moment.
          </p>
          <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
            <AnimatedButton
              onClick={handlePlanEventClick}
              variant="primary"
              size="lg"
              className="bg-[#a0224b] hover:bg-[#8a1e40]"
            >
              Plan Your Event
            </AnimatedButton>

            <AnimatedButton
              onClick={handleRentalsClick}
              variant="secondary"
              size="lg"
              className="border-white text-white hover:bg-white hover:text-[#30062f]"
            >
              View Our Rentals
            </AnimatedButton>
          </div>
        </div>
      </div>
    </section>;
}
