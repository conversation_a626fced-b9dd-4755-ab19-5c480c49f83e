import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Palette, Package, Camera, Music, Utensils, Coffee, PartyPopper, Briefcase, Heart, Users } from 'lucide-react';
import { useServices } from '../hooks/useContentful';
import { ServicesSEO } from '../components/SEO';

// Icon mapping for dynamic icons
const iconMap = {
  briefcase: Briefcase,
  heart: Heart,
  'party-popper': PartyPopper,
  users: Users,
  calendar: Calendar,
  palette: Palette,
  package: Package,
  camera: Camera,
  music: Music,
  utensils: Utensils,
  coffee: Coffee,
};

export function Services() {
  const { services, loading, error } = useServices();

  return (
    <>
      <ServicesSEO />
      {/* Services Hero */}
      <section className="relative w-full bg-cover bg-center py-20 md:py-28 bg-[#30062f]" style={{
        backgroundImage: `linear-gradient(rgba(48, 6, 47, 0.4), rgba(48, 6, 47, 0.4)), url('https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`,
        backgroundPosition: 'center',
        backgroundSize: 'cover'
      }}>
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Our Services
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            From planning to execution, we offer comprehensive event services to make your vision a reality
          </p>
        </div>
      </section>

      {/* Main Services */}
      <section className="py-16 md:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-6">
              What We Offer
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We provide comprehensive event services to bring your vision to life,
              from initial planning to final execution.
            </p>
          </div>
          
          {/* Main Service - Event Planning & Coordination */}
          <div className="flex justify-center mb-16">
            <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 flex flex-col items-center text-center hover:shadow-xl transition-shadow max-w-md">
              <div className="bg-[#30062f] p-4 rounded-full mb-6">
                <Calendar size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#30062f] mb-3">Event Planning & Coordination</h3>
              <p className="text-gray-600 mb-4">
                End-to-end event planning from concept to execution. We handle all the details
                so you can focus on enjoying your special day.
              </p>
              <ul className="text-sm text-gray-500 text-left space-y-1">
                <li>• Timeline development</li>
                <li>• Vendor coordination</li>
                <li>• Budget management</li>
                <li>• Day-of coordination</li>
              </ul>
            </div>
          </div>

          {/* View Packages Button */}
          <div className="text-center mt-12">
            <Link
              to="/packages"
              className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors inline-flex items-center"
            >
              <Package className="mr-2" size={20} />
              View Our Packages
            </Link>
          </div>
        </div>
      </section>

      {/* Dynamic Services from Contentful */}
      <section className="py-16 md:py-24 bg-[#F9F5FF]">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-6">
              Additional Services
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Comprehensive event services to bring your vision to life.
            </p>
          </div>
          
          {loading && (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#30062f]"></div>
              <p className="mt-2 text-gray-600">Loading services...</p>
            </div>
          )}
          
          {error && (
            <div className="text-center py-8">
              <p className="text-red-600">Error loading services: {error}</p>
              <p className="text-gray-600 mt-2">Displaying fallback content...</p>
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.filter(service => service.active).map((service) => {
              const IconComponent = iconMap[service.icon] || Calendar;
              return (
                <div key={service.id} className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 flex flex-col items-center text-center hover:shadow-xl transition-shadow">
                  <div className="bg-[#30062f] p-4 rounded-full mb-6">
                    <IconComponent size={32} className="text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-[#30062f] mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">
                    {service.description}
                  </p>
                  {service.features && service.features.length > 0 && (
                    <ul className="text-sm text-gray-500 text-left space-y-1 mb-4">
                      {service.features.map((feature, index) => (
                        <li key={index}>• {feature}</li>
                      ))}
                    </ul>
                  )}

                  {/* Add rental button for Event Rentals service */}
                  {(service.title.toLowerCase().includes('rental') ||
                    service.title.toLowerCase().includes('equipment') ||
                    service.description.toLowerCase().includes('rental')) && (
                    <div className="mt-4">
                      <Link
                        to="/rentals"
                        onClick={() => window.scrollTo({top: 0, behavior: 'smooth'})}
                        className="inline-flex items-center bg-[#a0224b] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#8a1e40] transition-colors"
                      >
                        <Package size={16} className="mr-2" />
                        View Our Rental Items
                      </Link>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-[#30062f] to-[#4a0a4b]">
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to bring your event vision to life?
          </h2>
          <p className="text-xl text-white/80 mb-8 max-w-3xl mx-auto">
            Let's discuss your event needs and create something extraordinary together.
          </p>
          <Link
            to="/contact"
            className="bg-white text-[#30062f] px-8 py-3 rounded-md font-medium hover:bg-gray-100 transition-colors inline-flex items-center"
            onClick={() => window.scrollTo({top: 0, behavior: 'smooth'})}
          >
            <Calendar className="mr-2" size={20} />
            Get Started Today
          </Link>
        </div>
      </section>
    </>
  );
}
