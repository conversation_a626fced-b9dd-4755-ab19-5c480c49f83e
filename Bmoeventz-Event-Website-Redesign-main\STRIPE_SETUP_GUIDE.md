# 🚀 Stripe Integration Setup Guide

## 📦 Required Dependencies

Run these commands to install the necessary packages:

```bash
npm install @stripe/stripe-js @stripe/react-stripe-js
npm install react-helmet-async
```

## 🔑 Environment Variables

Create or update your `.env` file with these variables:

```env
# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here

# Existing Contentful Configuration
VITE_CONTENTFUL_SPACE_ID=your_space_id
VITE_CONTENTFUL_ACCESS_TOKEN=your_access_token
```

## 🏗️ Backend API Endpoint Required

You'll need to create a backend endpoint at `/api/create-payment-intent` that:

### Example Node.js/Express Implementation:

```javascript
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

app.post('/api/create-payment-intent', async (req, res) => {
  try {
    const { amount, currency, items, customer } = req.body;

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amount, // Amount in pence (£10.00 = 1000)
      currency: currency || 'gbp',
      metadata: {
        customer_name: customer.name,
        customer_email: customer.email,
        customer_phone: customer.phone,
        event_date: customer.eventDate,
        event_type: customer.eventType,
        items: JSON.stringify(items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          days: item.rentalDays,
          total: item.totalPrice
        })))
      }
    });

    res.json({
      client_secret: paymentIntent.client_secret
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

## 🎯 Stripe Account Setup

1. **Create Stripe Account**: https://stripe.com
2. **Get API Keys**: Dashboard → Developers → API Keys
3. **Set up Webhooks**: Dashboard → Developers → Webhooks
4. **Configure Business Details**: Dashboard → Settings

### Recommended Webhook Events:
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `charge.dispute.created`

## 🔒 Security Considerations

1. **Never expose secret keys** in frontend code
2. **Use HTTPS** in production
3. **Validate webhook signatures**
4. **Store sensitive data securely**

## 🧪 Testing

### Test Card Numbers:
- **Successful payment**: 4242 4242 4242 4242
- **Declined payment**: 4000 0000 0000 0002
- **Requires authentication**: 4000 0025 0000 3155

### Test Details:
- **Expiry**: Any future date
- **CVC**: Any 3 digits
- **ZIP**: Any valid postal code

## 📱 Features Implemented

### ✅ Frontend Features:
- **Secure checkout form** with customer details
- **Card payment processing** with Stripe Elements
- **Order summary** with rental details
- **Success/error handling**
- **Responsive design**

### ✅ Customer Information Collected:
- Full name, email, phone
- Delivery address
- Event date and type
- Special requests
- Rental duration and quantities

### ✅ Payment Processing:
- **Secure card processing** via Stripe
- **Real-time validation**
- **Error handling**
- **Success confirmation**

## 🚀 Go Live Checklist

1. **Replace test keys** with live keys
2. **Set up production webhook endpoint**
3. **Configure business settings** in Stripe
4. **Test with real cards** (small amounts)
5. **Set up monitoring** and alerts
6. **Configure tax settings** if required

## 📞 Support

- **Stripe Documentation**: https://stripe.com/docs
- **Stripe Support**: https://support.stripe.com
- **Test your integration**: https://stripe.com/docs/testing
