import { useQuery, useMutation } from '@apollo/client';
import { 
  GET_SERVICES, 
  GET_EVENTS, 
  GET_GALLERY, 
  GET_PAGE_BY_SLUG, 
  GET_POSTS,
  SUBMIT_CONTACT_FORM 
} from '../lib/queries';

// Hook for fetching services
export const useServices = () => {
  const { data, loading, error } = useQuery(GET_SERVICES);
  return {
    services: data?.services?.nodes || [],
    loading,
    error
  };
};

// Hook for fetching events
export const useEvents = () => {
  const { data, loading, error } = useQuery(GET_EVENTS);
  return {
    events: data?.events?.nodes || [],
    loading,
    error
  };
};

// Hook for fetching gallery
export const useGallery = () => {
  const { data, loading, error } = useQuery(GET_GALLERY);
  return {
    gallery: data?.galleryItems?.nodes || [],
    loading,
    error
  };
};

// Hook for fetching a specific page
export const usePage = (slug: string) => {
  const { data, loading, error } = useQuery(GET_PAGE_BY_SLUG, {
    variables: { slug }
  });
  return {
    page: data?.pageBy,
    loading,
    error
  };
};

// Hook for fetching posts
export const usePosts = (first: number = 10) => {
  const { data, loading, error } = useQuery(GET_POSTS, {
    variables: { first }
  });
  return {
    posts: data?.posts?.nodes || [],
    loading,
    error
  };
};

// Hook for contact form submission
export const useContactForm = () => {
  const [submitForm, { data, loading, error }] = useMutation(SUBMIT_CONTACT_FORM);
  
  const submitContactForm = async (formData: {
    name: string;
    email: string;
    phone?: string;
    message: string;
  }) => {
    try {
      const result = await submitForm({
        variables: {
          input: formData
        }
      });
      return result.data?.submitContactForm;
    } catch (err) {
      console.error('Contact form submission error:', err);
      throw err;
    }
  };

  return {
    submitContactForm,
    loading,
    error,
    result: data?.submitContactForm
  };
};
