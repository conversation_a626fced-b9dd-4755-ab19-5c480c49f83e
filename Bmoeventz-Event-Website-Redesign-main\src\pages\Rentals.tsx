import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Package, Search, Filter, Star, Clock, CheckCircle, ShoppingCart, Plus } from 'lucide-react';
import { useCart } from '../contexts/CartContext';
import { RentalModal } from '../components/RentalModal';
import { RentalsSEO } from '../components/SEO';
import { useGoogleAnalytics } from '../components/GoogleAnalytics';
import { Breadcrumb, breadcrumbConfigs } from '../components/Breadcrumb';
import { useRentalItems } from '../hooks/useContentful';

export function Rentals() {
  // Dynamic rental items from Contentful
  const { rentalItems, loading, error } = useRentalItems();

  const { state: cartState, openCart } = useCart();
  const { trackAddToCart } = useGoogleAnalytics();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedItem, setSelectedItem] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleAddToCart = (item) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setSelectedItem(null);
    setIsModalOpen(false);
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Static categories for filtering
  const categories = ['All', 'Tables & Chairs', 'Linens & Decor', 'Lighting', 'Audio Visual', 'Catering Equipment'];

  // Filter items based on search and category
  const filteredItems = rentalItems ? rentalItems.filter(item => {
    if (!item) return false;

    const matchesSearch = !searchTerm ||
      (item.name && item.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Clean up category values by removing extra quotes and trimming whitespace
    const cleanItemCategory = item.category && typeof item.category === 'string'
      ? item.category.replace(/['"]/g, '').trim()
      : String(item.category || '').trim();
    const cleanSelectedCategory = selectedCategory.replace(/['"]/g, '').trim();

    const matchesCategory = selectedCategory === 'All' || cleanItemCategory === cleanSelectedCategory;

    // Debug category filtering (remove this in production)
    // console.log('🔍 Category Filter Debug:', { ... });

    return matchesSearch && matchesCategory && item.active;
  }) : [];

  // Additional debug info (remove this in production)
  // console.log('📊 Filter Summary:', { ... });

  return (
    <>
      <RentalsSEO />
      <Breadcrumb items={breadcrumbConfigs.rentals} />
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        {/* Floating Cart Button */}
      {cartState.totalItems > 0 && (
        <button
          onClick={openCart}
          className="fixed bottom-6 right-6 bg-[#30062f] text-white p-4 rounded-full shadow-lg hover:bg-[#4a0a4b] transition-colors z-30 flex items-center gap-2"
        >
          <ShoppingCart size={24} />
          <span className="bg-[#a0224b] text-white text-sm px-2 py-1 rounded-full min-w-[24px] h-6 flex items-center justify-center">
            {cartState.totalItems}
          </span>
        </button>
      )}

      {/* Hero Section */}
      <section className="relative w-full bg-cover bg-center py-20 md:py-28 bg-[#30062f]" style={{
        backgroundImage: `linear-gradient(rgba(48, 6, 47, 0.4), rgba(48, 6, 47, 0.4)), url('https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`,
        backgroundPosition: 'center',
        backgroundSize: 'cover'
      }}>
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Event Rental Shop
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            Premium quality rentals to elevate your event experience. Browse, configure, and rent with ease.
          </p>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Search Bar */}
            <div className="relative flex-1 max-w-md">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search rental items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#30062f]"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full transition-colors ${
                    selectedCategory === category
                      ? 'bg-[#30062f] text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 md:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          {loading && (
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#30062f]"></div>
              <p className="mt-4 text-gray-600">Loading rental items...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <p className="text-red-600">Error loading rental items: {error}</p>
            </div>
          )}

          {/* Empty State - Show when no items */}
          {!loading && !error && rentalItems.length === 0 && (
            <div className="text-center py-16">
              <div className="bg-[#30062f] p-6 rounded-full mx-auto mb-6 w-24 h-24 flex items-center justify-center">
                <Package size={48} className="text-white" />
              </div>
              <h3 className="text-2xl font-bold text-[#30062f] mb-4">
                Rental Catalog Coming Soon
              </h3>
              <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                We're currently updating our rental inventory with detailed descriptions,
                pricing, and high-quality images. Our comprehensive catalog will be available soon!
              </p>
              <Link
                to="/contact"
                onClick={scrollToTop}
                className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors inline-block"
              >
                Contact Us for Current Availability
              </Link>
            </div>
          )}

          {/* Rental Items Grid - Will show when items are available */}
          {!loading && !error && filteredItems.length > 0 && (
            <>
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-4">
                  Available Rental Items
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredItems.map((item) => (
                  <div key={item.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    {/* Item Image */}
                    <div className="h-40 bg-gray-200 overflow-hidden">
                      {item.image ? (
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-[#30062f]/10">
                          <Package size={48} className="text-[#30062f]/50" />
                        </div>
                      )}
                    </div>

                    {/* Item Details */}
                    <div className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-lg font-bold text-[#30062f] leading-tight">{item.name}</h3>
                        {item.featured && (
                          <Star size={16} className="text-[#c3bc70] fill-current flex-shrink-0 ml-2" />
                        )}
                      </div>

                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{item.description}</p>

                      <div className="flex items-center justify-between mb-3">
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {item.category}
                        </span>
                        {item.price && (
                          <span className="text-base font-bold text-[#30062f]">
                            {item.price}
                          </span>
                        )}
                      </div>

                      {item.availability && (
                        <div className="flex items-center text-xs text-gray-600 mb-3">
                          <Clock size={14} className="mr-1" />
                          {item.availability}
                        </div>
                      )}

                      {/* Add to Cart Button with Availability Logic */}
                      {item.availability && item.availability.toLowerCase().includes('available') ? (
                        <button
                          onClick={() => handleAddToCart(item)}
                          className="w-full bg-[#a0224b] text-white py-2 px-3 rounded-md text-sm font-medium hover:bg-[#8a1e40] transition-colors flex items-center justify-center gap-2"
                        >
                          <Plus size={14} />
                          Add to Cart
                        </button>
                      ) : (
                        <button
                          disabled
                          className="w-full bg-gray-300 text-gray-500 py-2 px-3 rounded-md text-sm font-medium cursor-not-allowed flex items-center justify-center gap-2 opacity-60"
                        >
                          <Clock size={14} />
                          {item.availability || 'Currently Unavailable'}
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-[#30062f] to-[#4a0a4b]">
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Need Something Specific?
          </h2>
          <p className="text-lg text-white/80 mb-8 max-w-3xl mx-auto">
            Can't find what you're looking for? We have access to a wide network of rental partners
            and can source almost anything for your event.
          </p>
          <Link
            to="/contact"
            onClick={scrollToTop}
            className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors inline-block"
          >
            Contact Us for Custom Requests
          </Link>
        </div>
      </section>

      {/* Rental Modal */}
      <RentalModal
        item={selectedItem}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
      </div>
    </>
  );
}
