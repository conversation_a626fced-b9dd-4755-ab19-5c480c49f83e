import React, { memo } from 'react';
import { <PERSON> } from 'react-router-dom';
import { PhoneCall, FileText, Users, Brush, ClipboardCheck, MessageSquare } from 'lucide-react';
export function EventsPlanning() {
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  const steps = [{
    number: 1,
    title: 'Discovery & Vision Call',
    description: 'We begin with a collaborative call to understand your goals, style, and budget. This is where your vision takes shape.',
    icon: <PhoneCall size={24} className="text-white" />,
    image: 'https://images.unsplash.com/photo-1600880292089-90a7e086ee0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    alt: 'Client consultation or vision board image'
  }, {
    number: 2,
    title: 'Proposal & Concept Development',
    description: "You'll receive a tailored proposal including services, timeline, and concept. Once approved, we begin coordination.",
    icon: <FileText size={24} className="text-white" />,
    image: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    alt: 'Event deck or moodboard example'
  }, {
    number: 3,
    title: 'Coordination & Vendor Liaison',
    description: "We handle timelines, vendor booking, logistics, and styling details so you don't have to. You stay updated but never overwhelmed.",
    icon: <Users size={24} className="text-white" />,
    image: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    alt: 'Team briefing or vendor walkthrough'
  }, {
    number: 4,
    title: 'Styling & Rentals Setup',
    description: 'Our styling team sets the scene with premium rentals and decor that aligns with your theme.',
    icon: <Brush size={24} className="text-white" />,
    image: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    alt: 'Event setup shot (throne chair, signage, backdrops)'
  }, {
    number: 5,
    title: 'On-the-Day Execution',
    description: 'From guest welcome to final pack-down, we manage everything. You just arrive and enjoy.',
    icon: <ClipboardCheck size={24} className="text-white" />,
    image: 'https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    alt: 'Coordinator with clipboard or guest arrival'
  }, {
    number: 6,
    title: 'Post-Event Debrief (Optional)',
    description: 'We offer a short debrief and event feedback loop to ensure ongoing excellence.',
    icon: <MessageSquare size={24} className="text-white" />,
    image: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    alt: 'Event wrap-up photo or smiling client'
  }];
  return <>
      {/* Hero Section */}
      <section className="relative w-full bg-cover bg-center py-20 md:py-28 bg-[#30062f]" style={{
      backgroundImage: `linear-gradient(rgba(48, 6, 47, 0.4), rgba(48, 6, 47, 0.4)), url('https://images.unsplash.com/photo-1505236858219-8359eb29e329?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`,
      backgroundPosition: 'center',
      backgroundSize: 'cover'
    }}>
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            How We Bring Your Event to Life
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            From Vision to Celebration — Here's How We Work With You
          </p>
        </div>
      </section>

      {/* Process Timeline */}
      <section className="py-16 md:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="mb-16 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-4">
              Our Event Planning Process
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We've refined our process to ensure your event is meticulously
              planned and flawlessly executed from start to finish.
            </p>
          </div>
          <div className="space-y-24">
            {steps.map((step, index) => <div key={index} className={`flex flex-col ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'} gap-8 md:gap-12 items-center`}>
                <div className="w-full md:w-1/2">
                  <div className="relative">
                    <div className="absolute -left-4 -top-4 md:-left-6 md:-top-6 bg-[#30062f] rounded-full w-12 h-12 md:w-16 md:h-16 flex items-center justify-center text-white text-xl md:text-2xl font-bold">
                      {step.number}
                    </div>
                    <div className="bg-[#30062f] rounded-lg overflow-hidden">
                      <img src={step.image} alt={step.alt} className="w-full h-64 md:h-80 object-cover" />
                    </div>
                  </div>
                </div>
                <div className="w-full md:w-1/2">
                  <div className="bg-[#30062f] p-2 rounded-full inline-flex items-center mb-4">
                    <div className="p-2">{step.icon}</div>
                    <span className="text-white font-medium px-3">
                      Step {step.number}
                    </span>
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold text-[#30062f] mb-4">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 text-lg mb-6">
                    {step.description}
                  </p>
                  {step.number === steps.length && <Link to="/contact" className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors inline-block" onClick={scrollToTop}>
                      Start Your Event Journey
                    </Link>}
                </div>
              </div>)}
          </div>
        </div>
      </section>
      {/* Rental Brochure Section */}
      <section className="py-16 bg-[#F9F5FF]">
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-6">
            Explore Our Event Planning Resources
          </h2>
          <p className="text-lg text-gray-600 mb-12 max-w-3xl mx-auto">
            Access our premium collection of resources to help you visualize and
            plan your perfect event.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
              <div className="bg-[#30062f] p-4 rounded-full mx-auto mb-6 w-16 h-16 flex items-center justify-center">
                <FileText size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#30062f] mb-3">
                Rental Brochure
              </h3>
              <p className="text-gray-600 mb-6">
                Explore our collection of premium event rentals to enhance your
                celebration.
              </p>
              <Link
                to="/banner"
                className="bg-[#a0224b] text-white px-6 py-2 rounded-md font-medium hover:bg-[#8a1e40] transition-colors inline-block"
              >
                View Brochure
              </Link>
            </div>
            <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
              <div className="bg-[#30062f] p-4 rounded-full mx-auto mb-6 w-16 h-16 flex items-center justify-center">
                <ClipboardCheck size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#30062f] mb-3">
                Planning Checklist
              </h3>
              <p className="text-gray-600 mb-6">
                A comprehensive checklist to ensure nothing is missed in your
                event planning process.
              </p>
              <button className="bg-[#a0224b] text-white px-6 py-2 rounded-md font-medium hover:bg-[#8a1e40] transition-colors">
                Get Checklist
              </button>
            </div>
            <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
              <div className="bg-[#30062f] p-4 rounded-full mx-auto mb-6 w-16 h-16 flex items-center justify-center">
                <Users size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-[#30062f] mb-3">
                Vendor Network
              </h3>
              <p className="text-gray-600 mb-6">
                Access our curated list of trusted vendors for every aspect of
                your event.
              </p>
              <button className="bg-[#a0224b] text-white px-6 py-2 rounded-md font-medium hover:bg-[#8a1e40] transition-colors">
                View Partners
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Video Showcase Section */}
      <section className="py-16 bg-[#F9F5FF]">
        <div className="max-w-7xl mx-auto px-6 md:px-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-[#30062f] mb-4">
              See Our Work in Action
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Watch how we transform visions into unforgettable experiences
            </p>
          </div>
          <div className="max-w-4xl mx-auto">
            <div className="relative w-full" style={{ paddingBottom: '56.25%' }}>
              <iframe
                className="absolute top-0 left-0 w-full h-full rounded-lg shadow-lg border-0"
                src="https://drive.google.com/file/d/1HDQ3fqjEyewdx8afedFw0Av_OH_YqgW8/preview"
                title="BMO Eventz - Event Planning Showcase"
                allow="autoplay"
                allowFullScreen
              ></iframe>
            </div>
            <div className="text-center mt-6">
              <a
                href="https://drive.google.com/file/d/1HDQ3fqjEyewdx8afedFw0Av_OH_YqgW8/view?usp=sharing"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-[#a0224b] hover:text-[#8a1e40] font-medium transition-colors"
              >
                Watch Full Video
                <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-[#30062f] to-[#4a0a4b]">
        <div className="max-w-7xl mx-auto px-6 md:px-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to start planning your perfect event?
          </h2>
          <p className="text-lg text-white/80 mb-8 max-w-3xl mx-auto">
            Contact us today to schedule your vision call and begin the journey
            to a stress-free, memorable event.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Link to="/contact" className="bg-[#a0224b] text-white px-8 py-3 rounded-md font-medium hover:bg-[#8a1e40] transition-colors" onClick={scrollToTop}>
              Request a Planning Call
            </Link>
            <Link to="/gallery" className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-md font-medium hover:bg-white hover:text-[#30062f] transition-colors" onClick={scrollToTop}>
              View Our Portfolio
            </Link>
          </div>
        </div>
      </section>
    </>;
}
